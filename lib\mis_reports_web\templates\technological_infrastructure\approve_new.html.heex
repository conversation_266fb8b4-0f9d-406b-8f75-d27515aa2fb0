

<style>
  *{
      margin: 0;
      padding: 0;
      box-sizing: border-box;

  }

  div.ex1 {
    height: 500px;
    overflow-y: scroll;
    margin-top:3%;
  }


  .bold{
      font-weight: 900;
  }
  .light{
      font-weight: 100;
  }
  .wrapper{
      background: #fff;
      padding: 30px;

  }
  .invoice_wrapper{
      width: 100%;
      max-width: 100%;
      border: 1px solid none
  }
  .invoice_wrapper .header .logo_invoice_wrap,
  .invoice_wrapper .header .bill_total_wrap{
      display:flex;
      justify-content: space-between;
      padding: 30px;
  }
  .invoice_wrapper .header .logo_sec .title_wrap{
      margin-left: 5px;
  }

  .invoice_wrapper .header .logo_sec .title_wrap .title{
     text-transform: uppercase ;
     font-size: 18px;
     color: #0C40A2;
  }


  .invoice_wrapper .header .logo_sec .title_wrap .sub_title{
     font-size: 12px;

  }
  .invoice_wrapper .header .invoice_sec,
  .invoice_wrapper .header .total_wrap{
      text-align: right;

  }
  .invoice_wrapper .header .invoice_sec .invoice{
      font-size: 28px;
      color:blue;

  }
  .invoice_wrapper .header .invoice_sec .invoice_no,
  .invoice_wrapper .header .invoice_sec .date{
      display: flex;
      width: 100%;

  }

  .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
  .invoice_wrapper .header .invoice_sec .date span:first-child{

      width: 70%;
      text-align: left;

  }

  .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
  .invoice_wrapper .header .invoice_sec .date span:first-child{

      width: calc(100%  -70px);
  }

  .invoice_wrapper .header .bill_total_wrap .name
  .invoice_wrapper .header .bill_total_wrap .price{
      font-size: 20px;


  }
  .invoice_wrapper .body .main_table .table_header{
      border-bottom: 1px solid #000;
  }

  .invoice_wrapper .body .main_table .table_header .row{
      color:#000;
      font-size: 18px;
      border-bottom: 0px;
  }

  .invoice_wrapper .body .main_table  .row{
      display: flex;
      border-bottom: 1px solid #e9e9e9;

  }
  .invoice_wrapper .body .main_table .row .col{
      padding: 9px;

  }
  .invoice_wrapper .body .main_table .row .col.col_no{width: 8%;}
  .invoice_wrapper .body .main_table .row .col.col_des{width: 30%;  text-align: center;}
  .invoice_wrapper .body .main_table .row .col.col_price{width: 23%; text-align: center;}
  .invoice_wrapper .body .main_table .row .col.col_qty{width: 21%;  text-align: center;}
  .invoice_wrapper .body .main_table .row .col.col_total{width: 20%;  text-align: center;}
  .invoice_wrapper .body .paymethod_grantotal_wrap{
      display: flex;
      justify-content: space-between;
      padding: 5px 0 30px;
      align-items: flex-end;
      padding-top: 2rem;

  }
  .invoice_wrapper .body .paymethod_grantotal_wrap .paymethod_sec{
     padding-left: 30px;
  }
  .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec{
     width: 20%;
  }
  .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p{
     display: flex;
     width: 100%;
     padding-bottom: 5px;
  }
  .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span{
   padding: 0 10px;
  }

  .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:first-child{
      width: 60%;
  }

  .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:last-child span{
      width: 40%;
      text-align: right;
  }

  .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p:last-child span{
      border-bottom: 1px solid #000;
  }

    .invoice_wrapper .footer{
     padding:30px;
  }

    .invoice_wrapper .footer{
     padding:30px;
  }

  .kanja
  {
     font-size:16px;

  }

  .kanja_peter
  {
     font-size:13px;
     margin-top:1rem

  }
  table {

      width: 130%;
      margin-left:2%;
  }


  table th {
  border: solid 1px gray;
  text-align: left;

  }

  .kanja_chileshe
  {
    font-weight: 100;
    background-color: #ffff99;
    width: 106px;
    height: 32px;
  }

  .man{
       background-color:#ffff99;
  }


  .kanja_p
  {
      font-weight:100;
      background-color:#c0c0c0;
  }

  .peter{
      text-align: right;
   }

   .p_kanja
  {
      font-weight:100;
      background-color:#ccccff;
  }
  .man{
  background-color:#ffcc99;

  }

  .mans{
  background-color:#aaf05a;

  }


   table,
      th,
      td {


          border: 0.5px solid gray;
      }

     
      .left{
          text-align: left;
      }

      
.class-name{
transition: all 1.5s;
animation: animation-name 0.5s linear;
}

@keyframes animation-name{
from{
transform: translateX(-100%);
height: 0px;
}
}

</style>
<body>
<div class="wrapper ex1 class-name">
  <div class="invoice_wrapper"  id="to_print">
    

 
      
    <.form :let={f} for={@changeset} as={:technological_infrastructure} id="technological_infrastructuret-form"  phx-submit="save" phx-target={@myself} >
        <div class="header pb-8">
            <div class="flex justify-between items-center w-full">
                <div class="description_wrap ml-6"  style="width: 200px;">
                    <label for="descript" class="block text-sm font-medium leading-6 text-gray-900">Description</label>
                    <div class="mt-1 w-48">
                      <%= text_input(
                        f,
                        :description,
                        autocomplete: "description",
                        placeholder: "Description",
                        class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                        required: "true",
                    disabled: "true"
                      ) %>
                      <%= error_tag(f, :descript) %>
                    </div>
                  </div>
                
                <div class="date_picker_wrap" style="width: 200px;">
                    <label for="report_date" class="block text-sm font-medium text-gray-700">Report Date</label>
                    <input 
                        type="date" 
                        id="report_date" 
                        name="technological_infrastructure[report_date]" 
                        value={get_in(@values, [:report_date] || 0)} 
                        required="required"
                         disabled ="true"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                </div>
            </div>
          </div>
        <div class="body" style="margin-top:2%">
          <div class="main_table">
             <table>
              <tr class="sample">
                      <th colspan="12" style="text-align:left; font-size:13px">Technological Infrastructure</th>
              </tr>
            <tr>
               <th style="font-size:13px"></th>
               <th style="font-size:13px">Lusaka</th>
               <th style="font-size:13px">Central</th>
               <th style="font-size:13px">Copperbelt</th>
              <th style="font-size:13px">Eastern</th>
              <th style="font-size:13px">Southern</th>
              <th style="font-size:13px">Luapula</th>
              <th style="font-size:13px">Western</th>
              <th style="font-size:13px">Northern</th>
              <th style="font-size:13px">North-western</th>
              <th style="font-size:13px">Muchinga</th>
              
            </tr>
            <tr>
             <td   style="text-align:left">Automated Teller Machines (Cash-in and Cash-out) </td>
              <td style="background-color: #ffff99;">
                <input class="kanja_chileshe" name="technological_infrastructure[lusaka][atm]" type="text" value={get_in(@values, [:lusaka, :atm] || 0) }  min="0"  disabled ="true">  
              </td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[copperbelt][atm]" type="text" value={get_in(@values, [:copperbelt, :atm] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[central][atm]" type="text" value={get_in(@values, [:central, :atm] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[luapula][atm]" type="text" value={get_in(@values, [:luapula, :atm] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[western][atm]" type="text" value={get_in(@values, [:western, :atm] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[northern][atm]" type="text" value={get_in(@values, [:lusaka, :atm] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[north_western][atm]" type="text" value={get_in(@values, [:copperbelt, :atm] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[southern][atm]" type="text" value={get_in(@values, [:central, :atm] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[muchinga][atm]" type="text" value={get_in(@values, [:luapula, :atm] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[eastern][atm]" type="text" value={get_in(@values, [:western, :atm] || 0) }  min="0" disabled ="true"></td>
            </tr>
            <tr>
              <td >Automated Teller Machines (Cash-out only)</td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[lusaka][atmco]" type="text" value={get_in(@values, [:lusaka, :atmco] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[copperbelt][atmco]" type="text" value={get_in(@values, [:copperbelt, :atmco] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[central][atmco]" type="text" value={get_in(@values, [:central, :atmco] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[luapula][atmco]" type="text" value={get_in(@values, [:luapula, :atmco] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[western][atmco]" type="text" value={get_in(@values, [:western, :atmco] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[northern][atmco]" type="text" value={get_in(@values, [:northern, :atmco] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[north_western][atmco]" type="text" value={get_in(@values, [:north_western, :atmco] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[southern][atmco]" type="text" value={get_in(@values, [:southern, :atmco] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[muchinga][atmco]" type="text" value={get_in(@values, [:muchinga, :atmco] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[eastern][atmco]" type="text" value={get_in(@values, [:eastern, :atmco] || 0) }  min="0" disabled ="true"></td>
            </tr>
              <tr>
             <td >Point of Sale (POS) (Number of Terminals) </td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[lusaka][pos]" type="text" value={get_in(@values, [:lusaka, :pos] || 0) }  min="0" disabled ="true"></td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[copperbelt][pos]" type="text" value={get_in(@values, [:copperbelt, :pos] || 0) }  min="0" disabled ="true"></td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[central][pos]" type="text" value={get_in(@values, [:central, :pos] || 0) }  min="0" disabled ="true"></td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[luapula][pos]" type="text" value={get_in(@values, [:luapula, :pos] || 0) } min="0" disabled ="true"></td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[western][pos]" type="text" value={get_in(@values, [:western, :pos] || 0) }  min="0" disabled ="true"></td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[northern][pos]" type="text" value={get_in(@values, [:northern, :pos] || 0) }  min="0" disabled ="true"></td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[north_western][pos]" type="text" value={get_in(@values, [:north_western, :pos] || 0) }  min="0" disabled ="true"></td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[southern][pos]" type="text" value={get_in(@values, [:southern, :pos] || 0) }  min="0" disabled ="true"></td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[muchinga][pos]" type="text" value={get_in(@values, [:muchinga, :pos] || 0) } min="0" disabled ="true"></td>
             <td><input class="kanja_chileshe" name="technological_infrastructure[eastern][pos]" type="text" value={get_in(@values, [:eastern, :pos] || 0) }  min="0" disabled ="true"></td>
            </tr>
            <tr>
              <td >Mobile Banking (Number of Subscribers)</td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[lusaka][mb]" type="text" value={get_in(@values, [:lusaka, :mb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[copperbelt][mb]" type="text" value={get_in(@values, [:copperbelt, :mb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[central][mb]" type="text" value={get_in(@values, [:central, :mb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[luapula][mb]" type="text" value={get_in(@values, [:luapula, :mb] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[western][mb]" type="text" value={get_in(@values, [:western, :mb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[northern][mb]" type="text" value={get_in(@values, [:northern, :mb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[north_western][mb]" type="text" value={get_in(@values, [:north_western, :mb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[southern][mb]" type="text" value={get_in(@values, [:southern, :mb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[muchinga][mb]" type="text" value={get_in(@values, [:muchinga, :mb] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[eastern][mb]" type="text" value={get_in(@values, [:eastern, :mb] || 0) }  min="0" disabled ="true"></td>
              
            </tr>
            <tr>
              <td >Internet Banking (Number of Subscribers)</td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[lusaka][ib]" type="text" value={get_in(@values, [:lusaka, :ib] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[copperbelt][ib]" type="text" value={get_in(@values, [:copperbelt, :ib] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[central][ib]" type="text" value={get_in(@values, [:central, :ib] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[luapula][ib]" type="text" value={get_in(@values, [:luapula, :ib] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[western][ib]" type="text" value={get_in(@values, [:western, :ib] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[northern][ib]" type="text" value={get_in(@values, [:northern, :ib] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[north_western][ib]" type="text" value={get_in(@values, [:north_western, :ib] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[southern][ib]" type="text" value={get_in(@values, [:southern, :ib] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[muchinga][ib]" type="text" value={get_in(@values, [:muchinga, :ib] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[eastern][ib]" type="text" value={get_in(@values, [:eastern, :ib] || 0) }  min="0" disabled ="true"></td>
            </tr>

            <tr>
              <td >Mobile Vehicular Branch</td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[lusaka][mvb]" type="text" value={get_in(@values, [:lusaka, :mvb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[copperbelt][mvb]" type="text" value={get_in(@values, [:copperbelt, :mvb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[central][mvb]" type="text" value={get_in(@values, [:central, :mvb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[luapula][mvb]" type="text" value={get_in(@values, [:luapula, :mvb] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[western][mvb]" type="text" value={get_in(@values, [:western, :mvb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[northern][mvb]" type="text" value={get_in(@values, [:northern, :mvb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[north_western][mvb]" type="text" value={get_in(@values, [:north_western, :mvb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[southern][mvb]" type="text" value={get_in(@values, [:southern, :mvb] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[muchinga][mvb]" type="text" value={get_in(@values, [:muchinga, :mvb] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[eastern][mvb]" type="text" value={get_in(@values, [:eastern, :mvb] || 0) }  min="0" disabled ="true"></td>
            </tr>

             <tr>
              <td >Others</td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[lusaka][others]" type="text" value={get_in(@values, [:lusaka, :others] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[copperbelt][others]" type="text" value={get_in(@values, [:copperbelt, :others] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[central][others]" type="text" value={get_in(@values, [:central, :others] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[luapula][others]" type="text" value={get_in(@values, [:luapula, :others] || 0) } min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[western][others]" type="text" value={get_in(@values, [:western, :others] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[northern][others]" type="text" value={get_in(@values, [:northern, :others] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[north_western][others]" type="text" value={get_in(@values, [:north_western, :others] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[southern][others]" type="text" value={get_in(@values, [:southern, :others] || 0) }  min="0" disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[muchinga][others]" type="text" value={get_in(@values, [:muchinga, :others] || 0) } min="0"  disabled ="true"></td>
              <td><input class="kanja_chileshe" name="technological_infrastructure[eastern][others]" type="text" value={get_in(@values, [:eastern, :others] || 0) }  min="0" disabled ="true"></td>
             
            </tr>
            

            
          </table>
          </div>

          <div class="mt-6 flex items-center justify-end gap-x-6">
            <button 
              type="submit" 
              phx-click="save"
              phx-target={@myself}
              phx-value-action="97"
              class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600">
              Approve
            </button>

            <button 
              type="submit"
              phx-click="save"
              phx-target={@myself}
              phx-value-action="96"
              class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
              Reject
            </button>
      </div>
      </div>
     </.form>






      
  </div>
</div>
</body>

