<style>
    *{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    }
    div.ex1 {
    height: 740px;
    overflow-y: scroll;
    margin-top:3%;
    }
    .bo
    font-weight: 900;
    }
    .light{
    font-weight: 100;
    }
    .wrapper{
    background: #fff;
    padding: 30px;
    }
    .invoice_wrapper{
    width: 100%;
    max-width: 100%;
    border: 1px solid none
    }
    .invoice_wrapper .header .logo_invoice_wrap,
    .invoice_wrapper .header .bill_total_wrap{
    display:flex;
    justify-content: space-between;
    padding: 30px;
    }
    .invoice_wrapper .header .logo_sec .title_wrap{
    margin-left: 5px;
    }
    .invoice_wrapper .header .logo_sec .title_wrap .title{
    text-transform: uppercase ;
    font-size: 18px;
    color: #0C40A2;
    }
    .invoice_wrapper .header .logo_sec .title_wrap .sub_title{
    font-size: 12px;
    }
    .invoice_wrapper .header .invoice_sec,
    .invoice_wrapper .header .total_wrap{
    text-align: right;
    }
    .invoice_wrapper .header .invoice_sec .invoice{
    font-size: 28px;
    color:blue;
    }
    .invoice_wrapper .header .invoice_sec .invoice_no,
    .invoice_wrapper .header .invoice_sec .date{
    display: flex;
    width: 100%;
    }
    .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
    .invoice_wrapper .header .invoice_sec .date span:first-child{
    width: 70%;
    text-align: left;
    }
    .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
    .invoice_wrapper .header .invoice_sec .date span:first-child{
    width: calc(100%  -70px);
    }
    .invoice_wrapper .header .bill_total_wrap .name
    .invoice_wrapper .header .bill_total_wrap .price{
    font-size: 20px;
    }
    .invoice_wrapper .body .main_table .table_header{
    border-bottom: 1px solid #000;
    }
    .invoice_wrapper .body .main_table .table_header .row{
    color:#000;
    font-size: 18px;
    border-bottom: 0px;
    }
    .invoice_wrapper .body .main_table  .row{
    display: flex;
    border-bottom: 1px solid #e9e9e9;
    }
    .invoice_wrapper .body .main_table .row .col{
    padding: 9px;
    }
    .invoice_wrapper .body .main_table .row .col.col_no{width: 8%;}
    .invoice_wrapper .body .main_table .row .col.col_des{width: 30%;  text-align: center;}
    .invoice_wrapper .body .main_table .row .col.col_price{width: 23%; text-align: center;}
    .invoice_wrapper .body .main_table .row .col.col_qty{width: 21%;  text-align: center;}
    .invoice_wrapper .body .main_table .row .col.col_total{width: 20%;  text-align: center;}
    .invoice_wrapper .body .paymethod_grantotal_wrap{
    display: flex;
    justify-content: space-between;
    padding: 5px 0 30px;
    align-items: flex-end;
    padding-top: 2rem;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .paymethod_sec{
    padding-left: 30px;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec{
    width: 20%;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p{
    display: flex;
    width: 100%;
    padding-bottom: 5px;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span{
    padding: 0 10px;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:first-child{
    width: 60%;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:last-child span{
    width: 40%;
    text-align: right;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p:last-child span{
    border-bottom: 1px solid #000;
    }
    .invoice_wrapper .footer{
    padding:30px;
    }
    .invoice_wrapper .footer{
    padding:30px;
    }
    .kanja
    {
    font-size:16px;
    }
    .kanja_peter
    {
    font-size:13px;
    margin-top:1rem
    }
    table {
    width: 150%;
    margin-left:2%;
    }
    table th {
    border: solid 1px gray;
    text-align: left;
    }
    .kanja_chileshe
    {
    font-weight:100;
    background-color:#ffff99;
    align-items: right;
    }
    .kanja_p
    {
    font-weight:100;
    background-color:#c0c0c0;
    }
    .p_kanja
    {
    font-weight:100;
    background-color:#ccccff;
    align-items: right;
    }
    .man{
    font-weight:400;
    }
    .mrt{
    margin-top:5%;
    font-weight:800
    }
    .mans{
    background-color:#aaf05a;
    }
    .yellow{
    background-color:#ffcc99;
    align-items: right;
    }
    .yellows{
    background-color:#ffff99;
    }
    th, td {
    padding: 2px;
    }
  </style>
   <body>
    <div class="wrapper ex1">
       <div class="invoice_wrapper"  id="to_print">
          <div class="header">
             <div class="logo_invoice_wrap">
                <div class="logo_sec">
                   <img src="">
                   
                </div>
                <div class="invoice_sec">
                   <p class="invoice bold"></p>
                   <p class="date" style="display:none">
                      <span class="bold">date</span>
                      <span><%= Timex.format!(Timex.local,"%e %b %Y", :strftime) %></span>
                   </p>
                </div>
             </div>
             <div class="grid grid-cols-2 gap-4">
              <div class="...">
                 <table style="width:100%">
                    <tr>
                          <th style="font-weight:500">Institutionional Code::</th>
                          <th class="kanja_p"><%= @settings.institution_code %></th>
                    </tr>
                      <tr>
                          <th style="font-weight:500">Compliance Start Date::</th>
                          <th class="kanja_p"><%= Timex.format!(Date.from_iso8601!(@filter_params["start_date"]),"%Y", :strftime) %></th>
                    </tr>
                      <tr>
                          <th style="font-weight:500">Compliance End Date::</th>
                          <th class="kanja_p"><%= Timex.format!(Date.from_iso8601!(@filter_params["end_date"]),"%Y", :strftime) %></th>
                    </tr>
                 </table>
              </div>
              <div class="...">
              </div>
           </div>
             <div class="bill_total_wrap">
                <div class="bill_sec">
                   <p class="mrt">BANK OF ZAMBIA</p>
                </div>
             </div>
          </div>
          <div class="body">
            <%= render "geographical_sum_of_loans.html", assigns %>

             <div class="main_table mt-5" >
               <p>Distribution according to Number of loans disbursed</p>
             </div>
             <%= render "geographical_count_of_loans.html", assigns %>
            
          </div>
       </div>
    </div>
  </body>

