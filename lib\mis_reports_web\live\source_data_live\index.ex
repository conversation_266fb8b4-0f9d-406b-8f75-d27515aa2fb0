defmodule MisReportsWeb.SourceDataLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component

  on_mount MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.UserController
  alias MisReports.{Workflow, Repo, SourceData, Accounts}
  alias MisReports.SourceData.{CustSegment, CustContribution, WeeklyReportSrcFile}
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Templates.DocStorage
  alias MisReports.SourceData.TrialBalScrFile

  @impl true
  def mount(params, _session, socket) do
    # Get reference and process details from params
    reference = params["reference"]
    process_id = params["process_id"]

    # Get parent reference if available
    parent_reference =
      if reference do
        Workflow.get_parent_reference(reference)
      else
        nil
      end

    # Get task statuses
    task_statuses = Workflow.get_task_statuses(parent_reference || reference)

    # Get user's role
    user_role = MisReports.Accounts.get_user_role!(socket.assigns.current_user.role_id)

    # Get all possible uploads based on user's role privileges
    all_available_uploads =
      MisReports.Enums.FileTypes.get_uploads_by_role_privilege(user_role.role_str)

    # Filter uploads to only show tasks that exist in header table
    available_uploads =
      if parent_reference do
        MisReports.Workflow.filter_existing_tasks(all_available_uploads, parent_reference)
      else
        all_available_uploads
      end

    # Add the role to the user record
    updated_user = Map.put(socket.assigns.current_user, :role, user_role)

    {:ok,
     socket
     |> assign(:data, %{"doc_name" => params["doc_name"]})
     |> assign(:data, %{"date" => params["date"]})
     |> assign(:uploaded_files, [])
     |> assign(:get_date, false)
     |> assign(:current_user, updated_user)
     |> assign(:available_uploads, available_uploads)
     |> assign(:get_end_date, false)
     |> assign(:reference, reference)
     |> assign(:process_id, process_id)
     |> assign(:parent_reference, parent_reference)
     |> assign(:task_statuses, task_statuses)
     |> assign(:current_path, socket.assigns.live_action)
     |> allow_upload(:excel, accept: ~w(.xlsx .xls .csv .zip), max_file_size: 200_000_000)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    menu_opts =
      LiveHelpers.menu_opts(__MODULE__, socket.assigns.live_action, [
        :edit,
        :new,
        :audit_log,
        :view,
        :show_trial_bal
      ])

    {:noreply,
     socket
     |> assign(menu_opts: menu_opts)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :trial_bal_upload, _params) do
    socket
    |> assign(page: %{prev: "Trial Balance", current: "Uploads"})
    |> allow_upload(:excel, accept: ~w(.xlsx .xls .csv .zip), max_file_size: 200_000_000)
    |> assign(:segementation, %CustSegment{})
  end

  defp apply_action(socket, :weekly_upload, _params) do
    socket
    |> assign(page: %{prev: "WEEKLY", current: "Uploads"})
    # |> allow_upload(:excel, accept: ~w(.xlsx .xls .csv .zip),  max_file_size: 200_000_000)
    |> allow_upload(:excel,
      accept: ~w(.xlsx .xls .csv .zip),
      max_entries: 1,
      max_file_size: 200_000_000
    )
    |> assign(:weekly_report_src_file, %WeeklyReportSrcFile{})
  end

  defp apply_action(socket, :new_temp, params) do
    socket
    |> assign(page: %{prev: "Source File", current: "Upload"})
    |> assign(:segementation, %CustSegment{})
  end

  defp apply_action(socket, :gbm_list_items, _params) do
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )
    |> list_cust_segementations()
  end

  defp apply_action(socket, :trial_bal_list, _params) do
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )
    |> trial_balance_list()
  end

  defp apply_action(socket, :weekly_upload_list, _params) do
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )
    |> weekly_upload_list()
  end

  defp apply_action(socket, :all_deal_list, _params) do
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )
    |> all_deal_list()
  end

  defp apply_action(socket, :ccr_list, _params) do
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )
    |> ccr_list()
  end

  defp apply_action(socket, :fx_cash_flow_list, _params) do
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )
    |> fx_cash_flow_list()
  end

  defp apply_action(socket, :upload_list, _params) do
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )
    |> upload_list()
  end

  defp apply_action(socket, :cust_acc_list, _params) do
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )
    |> cust_acc_list()
  end

  defp apply_action(socket, :show_trial_bal, %{"id" => id}) do
    all_entries = SourceData.get_trial_bal_entries(id)
    total = Enum.find(all_entries, fn x -> x.gl_no == "TOTAL" end)
    entries = Enum.filter(all_entries, fn x -> x.gl_no != "TOTAL" end)

    socket
    |> assign(page: %{prev: "Show", current: "Trial Balance"})
    |> assign(:segementation, %CustSegment{})
    |> assign(:trial_bal, entries)
    |> assign(:total, total)
  end

  defp apply_action(socket, :edit_ccr, %{"id" => id}) do
    contri = SourceData.get_cust_contribution!(id)

    socket
    |> assign(:contri, contri)
  end

  @impl true
  def handle_event("show", %{"id" => id} = _params, socket) do
    {:noreply, socket |> assign(:segementation, SourceData.get_cust_segment!(id))}
  end

  def handle_event("show_ccr", %{"id" => id} = _params, socket) do
    {:noreply, socket |> assign(:segementation, SourceData.get_cust_contribution!(id))}
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    case socket.assigns.live_action do
      :gbm_list_items ->
        {:noreply, assign(socket, page_size: page_size) |> list_cust_segementations()}

      :trial_bal_list ->
        {:noreply, assign(socket, page_size: page_size) |> trial_balance_list()}

      :all_deal_list ->
        {:noreply, assign(socket, page_size: page_size) |> all_deal_list()}

      :ccr_list ->
        {:noreply, assign(socket, page_size: page_size) |> ccr_list()}

      :fx_cash_flow_list ->
        {:noreply, assign(socket, page_size: page_size) |> fx_cash_flow_list()}

      :upload_list ->
        {:noreply, assign(socket, page_size: page_size) |> upload_list()}

      :weekly_upload_list ->
        {:noreply, assign(socket, page_size: page_size) |> weekly_upload_list()}
    end
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    case socket.assigns.live_action do
      # :gbm_list_items ->
      #   {:noreply, assign(socket, isearch: isearch) |> list_cust_segementations()}

      :trial_bal_list ->
        {:noreply, assign(socket, isearch: isearch) |> trial_balance_list()}

      :cust_acc_list ->
        {:noreply, assign(socket, isearch: isearch) |> cust_acc_list()}

      :weekly_upload_list ->
        {:noreply, assign(socket, isearch: isearch) |> weekly_upload_list()}

      _ ->
        {:noreply, assign(socket, isearch: isearch)}

        # :all_deal_list ->
        #   {:noreply, assign(socket, isearch: isearch) |> all_deal_list()}

        # :ccr_list ->
        #   {:noreply, assign(socket, isearch: isearch) |> ccr_list()}

        # :fx_cash_flow_list ->
        #   {:noreply, assign(socket, isearch: isearch) |> fx_cash_flow_list()}

        # :upload_list ->
        #   {:noreply, assign(socket, isearch: isearch) |> upload_list()}
    end
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    case socket.assigns.live_action do
      :gbm_list_items ->
        {:noreply, assign(socket, page: page) |> list_cust_segementations()}

      :trial_bal_list ->
        {:noreply, assign(socket, page: page) |> trial_balance_list()}

      :all_deal_list ->
        {:noreply, assign(socket, page: page) |> all_deal_list()}

      :ccr_list ->
        {:noreply, assign(socket, page: page) |> ccr_list()}

      :fx_cash_flow_list ->
        {:noreply, assign(socket, page: page) |> fx_cash_flow_list()}

      :upload_list ->
        {:noreply, assign(socket, page: page) |> upload_list()}

      :cust_acc_list ->
        {:noreply, assign(socket, page: page) |> cust_acc_list()}

      :weekly_upload_list ->
        {:noreply, assign(socket, page: page) |> weekly_upload_list()}
    end
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    {:noreply, handle_table_sort(socket.assigns.live_action, sort_field, dir, socket)}
  end

  # def handle_event("trial_bal_upload", %{"date" => date}, socket) do
  #   dir = MisReports.Utilities.directory_data()
  #   destin_path = dir.downloads

  #   consume_uploaded_entries(socket, :excel, fn %{path: path}, entry ->
  #     filename = "#{entry.client_name}"

  #     case File.exists?("#{destin_path}/#{filename}") or check_processed(dir, filename) do
  #       true ->
  #         {:postpone, {:error, "File already exists"}}

  #       false ->
  #         File.cp!(path, "#{destin_path}/#{filename}")
  #         handle_trial_upload(socket, filename, date)
  #     end
  #   end)
  #   |> case do
  #     [error: message] ->
  #       {:noreply, socket |> put_flash(:error, "#{message}")}

  #     [ok: message] ->
  #       {:noreply, socket |> put_flash(:info, "#{message}")}
  #   end
  # end

  # def handle_event("weekly_upload", %{"date" => date}, socket) do
  #   dir = MisReports.Utilities.directory_data()
  #   destin_path = dir.downloads

  #   consume_uploaded_entries(socket, :excel, fn %{path: path}, entry ->
  #     filename = "#{entry.client_name}"

  #     case File.exists?("#{destin_path}/#{filename}") or check_processed(dir, filename) do
  #       true ->
  #         {:postpone, {:error, "File already exists"}}

  #       false ->
  #         File.cp!(path, "#{destin_path}/#{filename}")
  #         handle_weekly_upload(socket, filename, date)
  #     end
  #   end)
  #   |> case do
  #     [error: message] ->
  #       {:noreply, socket |> put_flash(:error, "#{message}")}

  #     [ok: message] ->
  #       {:noreply, socket |> put_flash(:info, "#{message}")}
  #   end
  # end

  def handle_event("save", %{"doc_name" => doc_name} = params, socket) do
    dir = MisReports.Utilities.directory_data()
    destin_path = dir.downloads

    consume_uploaded_entries(socket, :excel, fn %{path: path}, entry ->
      filename = "#{doc_name}_DOC_NAME_#{entry.client_name}"

      with false <- MisReports.Templates.file_exists?(doc_name, filename),
           false <- File.exists?("#{destin_path}/#{filename}") or check_processed(dir, filename) do
        File.cp!(path, "#{destin_path}/#{filename}")

        cond do
          doc_name in ["TRIALBALANCEBS", "TRIALBALANCEIS"] ->
            selected_process = MisReports.Enums.FileTypes.get_uploads_by_value(doc_name)
            parentref = Workflow.get_parent_reference(socket.assigns.reference)

            # get correct reference based on selected file type
            childreference =
              Workflow.get_child_reference_by_code_and_parent_reference(
                selected_process.process_code,
                parentref
              )

            case handle_trial_upload(socket, filename, params["date"], childreference) do
              {:ok, _message} ->
                # Schedule reload after successful upload
                Process.send_after(self(), :reload_page, 1000)
                {:ok,
                 {:ok,
                  "File uploaded successfully. Data extraction and processing will start shortly. Reports will be ready in 5-15 minutes. Reference: #{childreference}"}}

              {:error, changeset} ->
                {:postpone,
                 {:error,
                  "File uploaded but workflow failed: #{traverse_errors(changeset.errors)}"}}

              error ->
                error
            end

          doc_name in [
            "WEEKLY_CCR",
            "WEEKLY_ALL_DEAL",
            "DAILY_ALL_DEAL",
            "DAILY_GUT",
            "FINACLE_TB"
          ] ->
            selected_process = MisReports.Enums.FileTypes.get_uploads_by_value(doc_name)
            parentref = Workflow.get_parent_reference(socket.assigns.reference)

            # get correct reference based on selected file type
            childreference =
              Workflow.get_child_reference_by_code_and_parent_reference(
                selected_process.process_code,
                parentref
              )

            case handle_weekly_upload(socket, filename, params["date"], childreference) do
              {:ok, _message} ->
               Process.send_after(self(), :reload_page, 1000)
                {:ok,
                 {:ok,
                  "File uploaded successfully. Data extraction and processing will start shortly. Reports will be ready in 5-15 minutes."}}

              {:error, changeset} ->
                {:postpone,
                 {:error,
                  "File uploaded but workflow failed: #{traverse_errors(changeset.errors)}"}}

              error ->
                error
            end

          true ->
            selected_process = MisReports.Enums.FileTypes.get_uploads_by_value(doc_name)
            parentref = Workflow.get_parent_reference(socket.assigns.reference)

            # get correct reference based on selected file type
            childreference =
              Workflow.get_child_reference_by_code_and_parent_reference(
                selected_process.process_code,
                parentref
              )

            case handle_upload_save(socket, filename, doc_name, params, childreference) do
              {:ok, _message} ->
                   Process.send_after(self(), :reload_page, 1000)
                {:ok,
                 {:ok,
                  "File uploaded successfully. Data extraction and processing will start shortly. Reports will be ready in 5-15 minutes. Reference: #{childreference}"}}

              {:error, changeset} ->
                {:postpone,
                 {:error,
                  "File uploaded but workflow failed: #{traverse_errors(changeset.errors)}"}}

              error ->
                error
            end
        end
      else
        _ ->
          {:postpone, {:error, "File already exists"}}
      end
    end)
    |> case do
      [error: message] ->
        {:noreply, socket |> put_flash(:error, "#{message}")}

      [ok: message] ->
        {:noreply, socket |> put_flash(:info, "#{message}")}
    end
  end

  @impl true
  def handle_event("update_status", %{"id" => _id} = params, socket) do
    put_conn_user(socket)
    |> UserController.update_status(params)
    |> case do
      {:ok, updated_user} ->
        message = %{message: %{info: "Operation successful"}}

        {:noreply,
         socket
         |> update(:users, fn users -> update_user_list(users, updated_user) end)
         |> push_event("notification", message)}

      {:error, changeset} ->
        error_msg = UserController.traverse_errors(changeset.errors) |> Enum.join("\r\n")
        {:noreply, push_event(socket, "notification", %{message: %{error: error_msg}})}
    end
  end

  @impl true
  def handle_event("reload_users", _params, socket) do
    {:noreply, push_redirect(socket, to: Routes.user_index_path(socket, :index))}
  end

  def handle_event("view_page", %{"_target" => ["add_option"], "add_option" => route}, socket) do
    {:noreply, push_patch(socket, to: route)}
  end

  def handle_event("view_page", %{"_target" => ["view_option"], "view_option" => route}, socket) do
    {:noreply, push_patch(socket, to: route)}
  end

  def handle_event("get-date", params, socket) do
    get_date =
      if params["doc_name"] in [
           "RISK_FILE",
           "SCHEDULE_20",
           "CMMP",
           "RISK_BSA",
           "NOSTRO",
           "CPRODUCTS",
           "CBRANCHES",
           "TRIALBALANCEBS",
           "TRIALBALANCEIS",
           "WEEKLY_CCR",
           "WEEKLY_ALL_DEAL",
           "DAILY_ALL_DEAL",
           "DAILY_GUT",
           "FINACLE_TB"
         ],
         do: true,
         else: false

    get_end_date = if params["doc_name"] in ["CMMP", "RISK_BSA"], do: true, else: false

    socket =
      assign(socket, :get_date, get_date)
      |> assign(:get_end_date, get_end_date)

    {:noreply, socket}
  end

  def handle_event("filter_table", params, socket) do
    case socket.assigns.live_action do
      :gbm_list_items ->
        {:noreply, assign(socket, isearch: params) |> list_cust_segementations()}

      :trial_bal_list ->
        {:noreply, assign(socket, isearch: params) |> trial_balance_list()}

      :all_deal_list ->
        {:noreply, assign(socket, isearch: params) |> all_deal_list()}

      :ccr_list ->
        {:noreply, assign(socket, isearch: params) |> ccr_list()}

      :fx_cash_flow_list ->
        {:noreply, assign(socket, isearch: params) |> fx_cash_flow_list()}

      :upload_list ->
        {:noreply, assign(socket, isearch: params) |> upload_list()}

      :cust_acc_list ->
        {:noreply, assign(socket, isearch: params) |> cust_acc_list()}

      :weekly_upload_list ->
        {:noreply, assign(socket, isearch: params) |> weekly_upload_list()}
    end
  end

  def handle_upload_save(socket, filename, doc_name, params, reference) do
    user_id = socket.assigns.current_user.id
    audit_msg = "uploaded source file \"#{filename}\" on #{Timex.today()}"

    # Add reference to the doc_storage record
    changeset =
      DocStorage.changeset(%DocStorage{}, %{
        doc_name: doc_name,
        doc_description: filename,
        date: params["date"],
        end_date: params["end_date"],
        maker_id: user_id,
        # Add this line
        reference: reference,
        status: "PENDING"
      })

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:insert_data, changeset)
    |> UserController.audit_log(user_id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, _result} ->
        # Schedule reload after successful upload
        Process.send_after(self(), :reload_page, 1000)
        {:ok,
         {:ok,
          "File uploaded successfully. Data extraction and processing will start shortly. Reports will be ready in 5-15 minutes. Reference: #{reference}"}}

      {:error, _failed_operation, multi} ->
        {:postpone, {:error, traverse_errors(multi)}}
    end
  end

  def handle_trial_upload(socket, filename, date, reference) do
    user_id = socket.assigns.current_user.id
    audit_msg = "uploaded Trial balance source file \"#{filename}\" on #{Timex.today()}"

    changeset =
      TrialBalScrFile.changeset(%TrialBalScrFile{}, %{
        date: date,
        filename: filename,
        year: String.slice(date, 0..3),
        month: String.slice(date, 5..6),
        reference: reference,
        maker_id: user_id
      })

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:insert_data, changeset)
    |> UserController.audit_log(user_id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, _result} ->
         Process.send_after(self(), :reload_page, 1000)
        {:ok,
         {:ok,
          "File uploaded successfully. Data extraction and processing will start shortly. Reports will be ready in 5-15 minutes."}}

      {:error, _failed_operation, multi} ->
        {:postpone, {:error, traverse_errors(multi)}}
    end
  end

  def handle_weekly_upload(socket, filename, date, reference) do
    user_id = socket.assigns.current_user.id
    audit_msg = "uploaded weekly report source file \"#{filename}\" on #{Timex.today()}"

    changeset =
      WeeklyReportSrcFile.changeset(%WeeklyReportSrcFile{}, %{
        date: date,
        filename: filename,
        year: String.slice(date, 0..3),
        month: String.slice(date, 5..6),
        reference: reference,
        maker_id: user_id
      })

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:insert_data, changeset)
    |> UserController.audit_log(user_id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, _result} ->
        Process.send_after(self(), :reload_page, 1000)
        {:ok,
         {:ok,
          "File uploaded successfully. Data extraction and processing will start shortly. Reports will be ready in 5-15 minutes."}}

      {:error, _failed_operation, multi} ->
        {:postpone, {:error, traverse_errors(multi)}}
    end
  end

  defp list_cust_segementations(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          SourceData.list_cust_segmentations(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, segementations: data.entries)
    |> assign(:segementation, %CustSegment{})
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp trial_balance_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          SourceData.trial_balance_list(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, trial_balances: data.entries)
    |> assign(:segementation, %CustSegment{})
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp weekly_upload_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          SourceData.weekly_upload_list(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, weekly_uploads: data.entries)
    |> assign(:weekly_report_src_file, %WeeklyReportSrcFile{})
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp all_deal_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          SourceData.all_deal_list(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, all_deals: data.entries)
    |> assign(:segementation, %CustSegment{})
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp ccr_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          SourceData.ccr_list(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, ccr_items: data.entries)
    |> assign(:segementation, %CustContribution{})
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp upload_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          MisReports.Templates.list_temp_sample(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, files: data.entries)
    |> assign(:segementation, %CustSegment{})
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp fx_cash_flow_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          SourceData.fx_cash_flow_list(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, fx_cash_items: data.entries)
    |> assign(:segementation, %CustSegment{})
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp cust_acc_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          MisReports.Utilities.cust_acc_list(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, accounts: data.entries)
    |> assign(:segementation, %CustSegment{})
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp update_user_list(users, %{id: id} = updated_user) do
    Enum.map(users, fn user ->
      if user.id == id do
        Accounts.with_assoc(updated_user, [:checker, :maker, :role])
      else
        user
      end
    end)
  end

  def handle_table_sort(:gbm_list_items, sort_field, dir, socket) do
    sort_by =
      Enum.find_value(
        [:country, :legal_entity, :month, :inserted_at, :ccy_code, :ccy_cat, :acc_bal_in_ccy],
        fn field ->
          if String.to_existing_atom(sort_field) == field do
            {String.to_existing_atom(dir), field}
          end
        end
      )

    socket
    |> assign(sort_by: sort_by)
    |> list_cust_segementations()
  end

  def handle_table_sort(:trial_bal_list, sort_field, dir, socket) do
    sort_by =
      Enum.find_value(
        [:inserted_at, :country, :status, :date],
        fn field ->
          if String.to_existing_atom(sort_field) == field do
            {String.to_existing_atom(dir), field}
          end
        end
      )

    socket
    |> assign(sort_by: sort_by)
    |> trial_balance_list()
  end

  def handle_table_sort(:all_deal_list, sort_field, dir, socket) do
    sort_by =
      Enum.find_value(
        [
          :inserted_at,
          :country,
          :prod_type,
          :trade_ccy,
          :counter_party,
          :coupon,
          :total_tenor,
          :issuer,
          :trade_id,
          :trade_dt
        ],
        fn field ->
          if String.to_existing_atom(sort_field) == field do
            {String.to_existing_atom(dir), field}
          end
        end
      )

    socket
    |> assign(sort_by: sort_by)
    |> all_deal_list()
  end

  def handle_table_sort(:ccr_list, sort_field, dir, socket) do
    sort_by =
      Enum.find_value(
        [
          :month_period,
          :year_period,
          :customer_name,
          :bal_sap_ledger_no,
          :currency_code,
          :actual_credit_balance,
          :account_maturity_date,
          :account_open_date,
          :account_number
        ],
        fn field ->
          if String.to_existing_atom(sort_field) == field do
            {String.to_existing_atom(dir), field}
          end
        end
      )

    socket
    |> assign(sort_by: sort_by)
    |> ccr_list()
  end

  def handle_table_sort(:fx_cash_flow_list, sort_field, dir, socket) do
    sort_by =
      Enum.find_value(
        [
          :country,
          :product_type,
          :trade_id,
          :book,
          :trade_date,
          :near_rate,
          :far_rate,
          :fx_swap_leg,
          :pay_ccy
        ],
        fn field ->
          if String.to_existing_atom(sort_field) == field do
            {String.to_existing_atom(dir), field}
          end
        end
      )

    socket
    |> assign(sort_by: sort_by)
    |> fx_cash_flow_list()
  end

  def handle_table_sort(:upload_list, sort_field, dir, socket) do
    sort_by =
      Enum.find_value(
        [:inserted_at, :doc_description, :status, :doc_name],
        fn field ->
          if String.to_existing_atom(sort_field) == field do
            {String.to_existing_atom(dir), field}
          end
        end
      )

    socket
    |> assign(sort_by: sort_by)
    |> upload_list()
  end

  def check_processed(dir, file) do
    date_path = Timex.format!(Timex.local(), "%Y/%b/%F", :strftime)
    prd_path = Path.join(dir.complete, date_path)
    dir = "#{Path.absname(String.trim(prd_path))}/#{Path.basename(file)}"
    File.exists?(dir)
  end

  def page_name(:new_temp), do: "Upload File"
  def page_name(:trial_bal_upload), do: "Trial Balance Upload"
  def page_name(:trial_bal_list), do: "Trial Balances"
  def page_name(:edit), do: "Edit Customer"
  def page_name(:audit_log), do: "Customer logs"
  def page_name(:gbm_list_items), do: "View GBM Entries"
  def page_name(:view), do: "Segementation Details"
  def page_name(:all_deal_list), do: "View All Deal Entries"
  def page_name(:ccr_list), do: "View CCR Entries"
  def page_name(:view_uploads), do: "view_uploads"
  def page_name(:weekly_upload), do: "Weekly Upload"
  def page_name(:fx_cash_flow_list), do: "View FX Cash Flow Entries"
  def page_name(:upload_list), do: "View Uploaded Files"
  def page_name(:edit_ccr), do: "View Uploaded Files"
  def page_name(:cust_acc_list), do: "View Uploaded Files"
  def page_name(:weekly_upload_list), do: "weekly Upload list"

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def traverse_errors(errors) do
    for {key, {msg, _opts}} <- errors, do: "#{key} #{msg}"
  end

  @impl true
  def handle_info({:file_completed, filename}, socket) do
    {:noreply,
     socket
     |> put_flash(:info, "File #{filename} processing completed")
     |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
  end

  def handle_progress(:excel, %{progress: 100}, socket) do
    Process.send_after(self(), :reload_page, 500)
    {:noreply, socket}
  end

  def handle_progress(:excel, _, socket), do: {:noreply, socket}

  def handle_info(:reload_page, socket) do
    # Get current URL parameters
    params = %{
      "process_id" => socket.assigns.process_id,
      "reference" => socket.assigns.reference,
      "step_id" => socket.assigns.step_id
    }

    # Construct the current path with parameters
    current_path = Routes.source_data_index_path(socket, :index, params)

    {:noreply, push_redirect(socket, to: current_path)}
  end
end
