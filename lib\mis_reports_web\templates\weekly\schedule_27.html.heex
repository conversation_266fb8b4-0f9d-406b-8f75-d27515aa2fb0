<style>
   *{
   margin: 0;
   padding: 0;
   box-sizing: border-box;
   }
   div.ex1 {
   height: 740px;
   overflow-y: scroll;
   margin-top:3%;
   }
   .bold{
   font-weight: 900;
   }
   .light{
   font-weight: 100;
   }
   .wrapper{
   background: #fff;
   padding: 30px;
   }
   .invoice_wrapper{
   width: 100%;
   max-width: 100%;
   border: 1px solid none
   }
   .invoice_wrapper .header .logo_invoice_wrap,
   .invoice_wrapper .header .bill_total_wrap{
   display:flex;
   justify-content: space-between;
   padding: 30px;
   }
   .invoice_wrapper .header .logo_sec .title_wrap{
   margin-left: 5px;
   }
   .invoice_wrapper .header .logo_sec .title_wrap .title{
   text-transform: uppercase ;
   font-size: 18px;
   color: #0C40A2;
   }
   .invoice_wrapper .header .logo_sec .title_wrap .sub_title{
   font-size: 12px;
   }
   .invoice_wrapper .header .invoice_sec,
   .invoice_wrapper .header .total_wrap{
   text-align: right;
   }
   .invoice_wrapper .header .invoice_sec .invoice{
   font-size: 28px;
   color:blue;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no,
   .invoice_wrapper .header .invoice_sec .date{
   display: flex;
   width: 100%;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
   .invoice_wrapper .header .invoice_sec .date span:first-child{
   width: 70%;
   text-align: left;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
   .invoice_wrapper .header .invoice_sec .date span:first-child{
   width: calc(100%  -70px);
   }
   .invoice_wrapper .header .bill_total_wrap .name
   .invoice_wrapper .header .bill_total_wrap .price{
   font-size: 20px;
   }
   .invoice_wrapper .body .main_table .table_header{
   border-bottom: 1px solid #000;
   }
   .invoice_wrapper .body .main_table .table_header .row{
   color:#000;
   font-size: 18px;
   border-bottom: 0px;
   }
   .invoice_wrapper .body .main_table  .row{
   display: flex;
   border-bottom: 1px solid #e9e9e9;
   }
   .invoice_wrapper .body .main_table .row .col{
   padding: 9px;
   }
   .invoice_wrapper .body .main_table .row .col.col_no{width: 8%;}
   .invoice_wrapper .body .main_table .row .col.col_des{width: 30%;  text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_price{width: 23%; text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_qty{width: 21%;  text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_total{width: 20%;  text-align: center;}
   .invoice_wrapper .body .paymethod_grantotal_wrap{
   display: flex;
   justify-content: space-between;
   padding: 5px 0 30px;
   align-items: flex-end;
   padding-top: 2rem;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .paymethod_sec{
   padding-left: 30px;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec{
   width: 20%;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p{
   display: flex;
   width: 100%;
   padding-bottom: 5px;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span{
   padding: 0 10px;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:first-child{
   width: 60%;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:last-child span{
   width: 40%;
   text-align: right;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p:last-child span{
   border-bottom: 1px solid #000;
   }
   .invoice_wrapper .footer{
   padding:30px;
   }
   .invoice_wrapper .footer{
   padding:30px;
   }
   .kanja
   {
   font-size:16px;
   }
     .peter{
   text-align: right;
   }
   .bold_peter{
   font-size:13px;
   font-weight:800
   }
   .kanja_peter
   {
   font-size:13px;
   margin-top:1rem
   }
   table {
   width: 100%;
   margin-left:2%;
   }
   table th {
   border: solid 1px gray;
   text-align: left;
   }
   .kanja_chileshe
   {
   font-weight:100;
   background-color:#ffff99;
   }
   .kanja_p
   {
   font-weight:100;
   background-color:#c0c0c0;
   }
   .p_kanja
   {
   font-weight:100;
   background-color:#ccccff;
   }
   .man{
   font-weght:400;
   }
   .mrt{
   margin-top:5%;
   font-weight:800
   }
   .mans{
   background-color:#aaf05a;
   }
   .yellow{
   background-color:#ffcc99;
   }
   .yellows{
   background-color:#ffff99;
   }
   th, td {
   padding: 2px;
   }
</style>
<body>
   <div class="wrapper ex1">
      <div class="invoice_wrapper"  id="to_print">
         <div class="header">
            <div class="logo_invoice_wrap">
               <div class="logo_sec">
                  <img src="">
                  <div class="title_wrap">
                     <p class="title bold">
                        Schedule 27
                     </p>
                     <p class="">
                       ZM-SCH27
                     </p>
                  </div>
               </div>
               <div class="invoice_sec">
                  <p class="invoice bold"></p>
                  <p class="date" style="display:none">
                     <span class="bold">date</span>
                     <span><%= Timex.format!(Timex.local,"%e %b %Y", :strftime) %></span>
                  </p>
               </div>
            </div>

            <table style="width:50%">
              <tr>
                  <th style="font-weight:500">Institutionional Code:</th>
                  <th class="kanja_chileshe text-center"><%= @settings.institution_code %></th>
               </tr>
               <tr>
  <th style="font-weight:500">Financial Year:</th>
  <th class="gray text-center">
    <%=
      date = cond do
        is_binary(@filter_params["date"]) -> Date.from_iso8601!(@filter_params["date"])
        is_struct(@filter_params["date"], Date) -> @filter_params["date"]
        true -> Date.utc_today()
      end
      Timex.format!(date, "%Y", :strftime)
    %>
  </th>
</tr>
<tr>
  <th style="font-weight:500">Report Date:</th>
  <th class="gray text-center">
    <%=
      date = cond do
        is_binary(@filter_params["date"]) -> Date.from_iso8601!(@filter_params["date"])
        is_struct(@filter_params["date"], Date) -> @filter_params["date"]
        true -> Date.utc_today()
      end
      Timex.format!(date, "%e %B %Y", :strftime)
    %>
  </th>
</tr>
            </table>
            <div class="bill_total_wrap">
               <div class="bill_sec">

                  <p class="mrt">BANK OF ZAMBIA</p>

               </div>
            </div>
         </div>

         <div class="body">
            <div class="main_table" >
               <table>

                  <thead>
                     <tr class="sample">
                        <th style="font-size:13px; font-weight:200">1</th>
                        <th  style="font-size:13px; font-weight:800">COMMERCIAL BANKS' WEEKLY RETURN OF SELECTED ASSETS AND LIABILITIES</th>
                     </tr>
                     <tr class="sample">
                        <th style="font-size:13px; font-weight:200">2</th>
                        <th  style="font-size:13px; font-weight:800">Position as on Wednesday:</th>
                     </tr>
                     <tr class="sample">
                        <th style="font-size:13px; font-weight:200">3</th>
                        <th  style="font-size:13px; font-weight:800">30/08/2023</th>
                        <th  style="font-size:13px; font-weight:800">Total in USD $</th>
                        <th  style="font-size:13px; font-weight:800">Total in Kwacha (ZMW)</th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:200">4</th>
                        <th style="font-size:13px; font-weight:600">(I.)  SELECTED ASSETS</th>
                        <th class=""></th>
                        <th class=""></th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:200">5</th>
                        <th style="font-size:13px; font-weight:200">
                          <div class="flex items-center justify-between">
                            <span>1.   Total Core Liquid Assets (a+b+c+d+e+f-g)</span>
                            <%= if not @view_only do %>
                              <button
                                type="button"
                                phx-click="open_comment_modal"
                                phx-value-schedule={"#{@reference}_total_core_liquid"}
                                class="comment-btn rounded-md bg-blue-600 px-3 py-1.5 text-xs font-medium text-white shadow-sm hover:bg-blue-700 inline-flex items-center gap-1"
                                title="Add comment for Total Core Liquid Assets"
                              >
                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                                </svg>
                                <span>Comment</span>
                              </button>
                            <% end %>
                          </div>
                        </th>
                        <th class=""></th>
                        <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:total_core_liquid]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">6</th>
                        <th style="font-size:13px; font-weight:200">(a)  Zambia Notes and Coins</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:zambia_notes_coins]%></th>
                     </tr>

                      <tr>
                        <th style="font-size:13px; font-weight:200">7</th>
                        <th style="font-size:13px; font-weight:200">(b)  Current Account Balances at BOZ</th>
                        <th class=""> </th>
                        <th class="kanja_chileshe peter"><%= @data[:current_account_boz]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">8</th>
                        <th style="font-size:13px; font-weight:200">   (c)  Treasury Bill Holdings at face value</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:tbills_holdings_at_face_value]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">9</th>
                        <th style="font-size:13px; font-weight:200">(d)  OMO Term Deposits</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:omo_term_deposits]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">10</th>
                        <th style="font-size:13px; font-weight:200">(e)  OMO Repo Placements</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:omo_repo_placements]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">11</th>
                        <th style="font-size:13px; font-weight:200">(f)  Collateralised Interbank Loans Made</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:col_interbank_loans_made]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">12</th>
                        <th style="font-size:13px; font-weight:200"> (g)  Collateralised Interbank Loans Received</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:col_interbank_loans_received]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">13</th>
                        <th style="font-size:13px; font-weight:200"></th>
                        <th class=""></th>
                        <th class="" style="color:#fff">-</th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">14</th>
                        <th style="font-size:13px; font-weight:200">2.   Kwacha Statutory Reserve Account Balances at BOZ</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:kwacha_statutory_reserve]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">13</th>
                        <th style="font-size:13px; font-weight:200"></th>
                        <th class=""></th>
                        <th class="" style="color:#fff">-</th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:200">15</th>
                        <th style="font-size:13px; font-weight:200">3.   Government securities (eligible for statutory reserve requirements): </th>
                        <th class=""></th>
                        <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:govt_securities]%></th>
                      </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">16</th>
                        <th style="font-size:13px; font-weight:200">(a) Government bond ISIN ZM1000006370 </th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:govt_bond_ZM1000006370]%></th>
                     </tr>

                      <tr>
                        <th style="font-size:13px; font-weight:200">17</th>
                        <th style="font-size:13px; font-weight:200">(b) Government bond ISIN ZM1000006388</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:govt_bond_ZM1000006388]%></th>
                     </tr>

                      <tr>
                        <th style="font-size:13px; font-weight:200">18</th>
                        <th style="font-size:13px; font-weight:200">(c) Government bond ISIN ZM1000006396</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:govt_bond_ZM1000006396]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">18</th>
                        <th style="font-size:13px; font-weight:200"></th>
                        <th class=""></th>
                        <th class="" style="color:#fff">-</th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">16</th>
                        <th style="font-size:13px; font-weight:200">3.   Foreign Currency  (FCY) Statutory Reserve Account Balances at BOZ (US $)</th>
                        <th class="kanja_chileshe peter"><%= @data[:fcy_statutory_reserve]%></th>
                        <th class=""></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">17</th>
                        <th style="font-size:13px; font-weight:200"></th>
                        <th class=""></th>
                        <th class="" style="color:#fff"></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">18</th>
                        <th style="font-size:13px; font-weight:200">4.   (a)  Kwacha Loans and Advances Outstanding</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:kwacha_loans_advances_outstanding]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">19</th>
                        <th style="font-size:13px; font-weight:200">(b)  Foreign Currency Loans and Advances Outstanding (US $)</th>
                        <th class="kanja_chileshe peter"><%= @data[:fycla_outstanding]%></th>
                        <th class=""></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">20</th>
                        <th style="font-size:13px; font-weight:200"></th>
                        <th class=""></th>
                        <th class="" style="color:#fff">-</th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">21</th>
                        <th style="font-size:13px; font-weight:200">5.  (a)  New Kwacha Loans to Agriculture</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:new_kwacha_loans_agriculture]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">22</th>
                        <th style="font-size:13px; font-weight:200"> (b)  New Foreign Currency  Loans to Agriculture (US $)</th>
                        <th class="kanja_chileshe peter"><%= @data[:new_fcy_loans_agriculture]%></th>
                        <th class=""></th>
                     </tr>


                     <tr>
                        <th style="font-size:13px; font-weight:200">23</th>
                        <th style="font-size:13px; font-weight:500">(II.)  SELECTED LIABILITIES</th>
                        <th class=""></th>
                        <th class="" style="color:#fff">-</th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">24</th>
                        <th style="font-size:13px; font-weight:500"></th>
                        <th class=""></th>
                        <th class="" style="color:#fff">-</th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">25</th>
                        <th style="font-size:13px; font-weight:500"></th>
                        <th class=""></th>
                        <th class="" style="color:#fff">-</th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">26</th>
                        <th style="font-size:13px; font-weight:200">6. Kwacha Deposit Liabilities to the Public  (a )+ (b) </th>
                        <th class=""></th>
                        <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:add_kwacha_deposit_liabilities_public]%></th>
                      </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">27</th>
                        <th style="font-size:13px; font-weight:200">(a)  Kwacha Deposit Liabilities to the Public </th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:kwacha_deposit_liabilities_public]%></th>
                     </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">28</th>
                        <th style="font-size:13px; font-weight:200">(b)  Bills Payable</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter">-</th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:200">29</th>
                        <th style="font-size:13px; font-weight:500"></th>
                        <th class=""></th>
                        <th class="" style="color:#fff">-</th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:200">30</th>
                        <th style="font-size:13px; font-weight:200">7.Total Kwacha Deposit Liabilities to Foreign Institutions (Vostro Accounts)</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:kwacha_deposit_foreign_vostro]%></th>
                      </tr>

                      <tr>
                        <th style="font-size:13px; font-weight:200">31</th>
                        <th style="font-size:13px; font-weight:200">8.Total Kwacha Deposit Liabilities to the Government (Govt Deposits)</th>
                        <th class=""></th>
                        <th class="kanja_chileshe peter"><%= @data[:total_kwacha_deposit_liabilities_govt]%></th>
                      </tr>

                     <tr>
                        <th style="font-size:13px; font-weight:200">32</th>
                        <th style="font-size:13px; font-weight:500"></th>
                        <th class=""></th>
                        <th class="" style="color:#fff">-</th>
                     </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">33</th>
                           <th style="font-size:13px; font-weight:200"> 9.     Foreign Currency Deposit Liabilities to the Public in US$</th>
                           <th class="kanja_chileshe peter"><%= @data[:fyc_deposit_liabilities_public]%></th>
                           <th class=""></th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">34</th>
                            <th style="font-size:13px; font-weight:200">10.    Foreign Currency Deposit Liabilities to Foreign Institutions (Vostro Accounts) in US$</th>
                            <th class="kanja_chileshe peter"><%= @data[:fcy_deposit_foreign_vostro]%></th>
                            <th class=""></th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">35</th>
                            <th style="font-size:13px; font-weight:200">11. Foreign Currency Deposit Liabilities to Government (Govt Deposits) in US$</th>
                            <th class="kanja_chileshe peter"><%= @data[:fcy_deposit_liabilities_govt]%></th>
                            <th class=""></th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">36</th>
                            <th style="font-size:13px; font-weight:500"></th>
                            <th class=""></th>
                            <th class="" style="color:#fff">-</th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">37</th>
                            <th style="font-size:13px; font-weight:200">12.    Kwacha  Deposit Liabilities to the Public, to Foreign Institutions and to Government, less New Agricultural  Lending in Kwacha  </th>
                            <th class=""></th>
                            <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:kwacha_deposit_foreign_govt_less_agr]%></th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">38</th>
                            <th style="font-size:13px; font-weight:500"></th>
                            <th class=""></th>
                            <th class="" style="color:#fff">-</th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">39</th>
                           <th style="font-size:13px; font-weight:200">13.    Foreign Currency Deposit Liabilities to the Public, to Foreign Institutions and to Government, less New Agricultural Lending in Foreign Currency. </th>
                           <th class="kanja_p peter " style="font-size:15px; font-weight:800"><%= @data[:fyc_deposit_foreign_govt_less_agr]%></th>
                           <th class=""></th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">40</th>
                            <th style="font-size:13px; font-weight:500">(III.)  COMPLIANCE RATIOS</th>
                            <th class=""></th>
                            <th class="" style="color:#fff">-</th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">41</th>
                           <th style="font-size:13px; font-weight:500"></th>
                           <th class=""></th>
                           <th class="" style="color:#fff">-</th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">42</th>
                           <th style="font-size:13px; font-weight:500">Core Liquid Asset Compliance</th>
                           <th class=""></th>
                           <th class="" style="color:#fff">-</th>
                        </tr>

                        <tr>
                        <th style="font-size:13px; font-weight:200">43</th>
                            <th style="font-size:13px; font-weight:200">14.    Core Liquid Asset Ratio (% ) ( 1 /6 )</th>
                            <th class=""></th>
                            <th class="kanja_p peter"><%= @data[:core_liquid_asset_ratio]%> %</th>
                        </tr>
                        <tr>
                           <th style="font-size:13px; font-weight:200">44</th>
                            <th style="font-size:13px; font-weight:200">15.    Minimum Required Core Liquid Assets (6% of 6) </th>
                            <th class=""></th>
                            <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:minimum_required_CLA]%></th>
                         </tr>

                         <tr>
                           <th style="font-size:13px; font-weight:200">45</th>
                           <th style="font-size:13px; font-weight:200">16.Excess/shortfall in Core Liquid Assets (1 - 15)</th>
                           <th class=""></th>
                           <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:excess_shortfall_CLA]%></th>
                         </tr>


                         <tr>
                           <th style="font-size:13px; font-weight:200">46</th>
                            <th style="font-size:13px; font-weight:500"></th>
                            <th class=""></th>
                            <th class="" style="color:#fff">-</th>
                        </tr>


                        <tr>
                           <th style="font-size:13px; font-weight:200">47</th>
                            <th style="font-size:13px; font-weight:500">Kwacha Statutory Reserves Compliance</th>
                            <th class=""></th>
                            <th class="" style="color:#fff">-</th>
                        </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">48</th>
                            <th style="font-size:13px; font-weight:200">17.Kwacha Statutory Reserve Ratio (% ) ( 2 / 12 )  </th>
                            <th class=""></th>
                            <th class="kanja_p peter"><%= @data[:kwacha_statutory_reserve_ratio]%> %</th>
                         </tr>

                         <tr>
                           <th style="font-size:13px; font-weight:200">49</th>
                            <th style="font-size:13px; font-weight:200">18. Minimum Kwacha Statutory Reserves (26% of 12)  </th>
                            <th class=""></th>
                            <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:minimum_KSR]%></th>
                         </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">50</th>
                            <th style="font-size:13px; font-weight:200">20.  Maximum Government securities (eligible for statutory reserve requirements)</th>
                            <th class=""></th>
                            <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:max_govt_securities]%></th>
                         </tr>

                        <tr>
                           <th style="font-size:13px; font-weight:200">50</th>
                            <th style="font-size:13px; font-weight:200">19. Excess/shortfall in Kwacha Statutory Reserves (2 - 18)</th>
                            <th class=""></th>
                            <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:excess_shortfall_KSR]%></th>
                         </tr>

                         <tr>
                           <th style="font-size:13px; font-weight:200">51</th>
                            <th style="font-size:13px; font-weight:500">Foreign Currency Statutory Reserves Compliance</th>
                            <th class=""></th>
                            <th class="" style="color:#fff">-</th>
                        </tr>


                         <tr>
                           <th style="font-size:13px; font-weight:200">52</th>
                            <th style="font-size:13px; font-weight:200">20.FCY (US $) Statutory Reserve Ratio (% ) ( 3 / 13 )</th>
                            <th class="kanja_p peter"><%= @data[:fcy_statutory_reserve_ratio]%> %</th>
                            <th class=""></th>
                         </tr>

                         <tr>
                           <th style="font-size:13px; font-weight:200">53</th>
                            <th style="font-size:13px; font-weight:200">21.Minimum FCY Statutory Reserves (26.0% of 13) </th>
                            <th class="kanja_p peter" style="font-size:15px; font-weight:800"><%= @data[:minimum_fcy_SR]%></th>
                            <th class=""></th>
                         </tr>

                         <tr>
                           <th style="font-size:13px; font-weight:200">54</th>
                            <th style="font-size:13px; font-weight:200">22.  Excess/shortfall in FCY Statutory Reserves(US $)  (3 - 21) </th>
                            <th class="kanja_p peter"><%= @data[:excess_shortfall_fyc_SR]%></th>
                            <th class=""></th>
                         </tr>
                   </thead>
               </table>
            </div>
         </div>
         <div class="footer" style="display:none">
            <p>INCOME STATEMENT</p>
            <div class="terms">
               <p>
                  terms and conditions
               </p>
               <p>lorem</p>
            </div>
         </div>
      </div>
   </div>
</body>