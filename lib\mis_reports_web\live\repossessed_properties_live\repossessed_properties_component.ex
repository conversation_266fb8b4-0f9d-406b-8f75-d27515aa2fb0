defmodule MisReportsWeb.RepossessedPropertiesLive.RepossessedPropertiesComponent do
  use MisReportsWeb, :live_component
  use PipeTo.Override
  alias MisReports.{Utilities, Repo}
  alias MisReports.Utilities.RepossedProp
  alias MisReportsWeb.UserController

  import MisReportsWeb.UserLive.Index, only: [put_conn_user: 1]

  @impl true
  def render(assigns) do
    ~H"""

    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
      <%= Phoenix.View.render(MisReportsWeb.RepossessedPropertiesView, "repossessed_property.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.RepossessedPropertiesView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{repossed_prop: repossed_prop} = assigns, socket) do
    changeset = Utilities.change_RepossedProp(repossed_prop)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"repossed_prop" => repossed_prop_params}, socket) do
    changeset =
      socket.assigns.repossed_prop
      |> RepossedProp.changeset(repossed_prop_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> save_repossed_prop(socket, :reject, params)
      "97" -> save_repossed_prop(socket, :approve, params)
      _ -> {:noreply, put_flash(socket, :error, "Invalid action")}
    end
  end

  @impl true
  def handle_event("save", %{"repossed_prop" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  defp handle_save(socket, :new, params) do
    audit_msg = "Created new Repossessed Property for date: #{params["report_date"]}"
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :repossed_prop,
      RepossedProp.changeset(
        %RepossedProp{maker_id: user.id},
        params
      )
    )
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{repossed_prop: repossed_prop}} ->
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Repossessed Property Creation"
             ) do
          {:ok, reference_number} ->
            case Utilities.update_repossed_prop(repossed_prop, %{reference: reference_number}) do
              {:ok, updated_repossed_prop} ->
                {:noreply,
                 socket
                 |> put_flash(
                   :info,
                   "Repossessed Property created successfully. Reference: #{reference_number}"
                 )
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  defp save_repossed_prop(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Repossessed Property Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp save_repossed_prop(socket, :approve, params) do
    repossed_prop = socket.assigns.repossed_prop
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Repossessed Property Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      RepossedProp.changeset(repossed_prop, %{
        status: "A",
        checker_id: current_user.id
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_prop}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve")
         |> assign(:changeset, failed_value)}
    end
  end

  def handle_update(socket, params, repossed_prop) do
    audit_msg = "Updated new Repossessed Property \"#{repossed_prop.report_date}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :repossed_prop,
      RepossedProp.changeset(
        repossed_prop,
        Map.merge(params, %{"status" => "D", "checker_id" => nil})
      )
    )
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{repossed_prop: repossed_prop, audit_log: _user_log}} ->
        {:ok, repossed_prop}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def traverse_errors(errors) do
    for {key, {msg, opts}} <- errors, into: %{} do
      msg =
        Regex.replace(~r"%{(\w+)}", msg, fn _, key ->
          opts |> Keyword.get(String.to_existing_atom(key), key) |> to_string()
        end)

      {key, msg}
    end
  end
end
