
defmodule MisReports.Workers.BozReq.Schedule18d do

  def perform(item) do
    decoded_item =
      case item.schedule_18d do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end


    IO.inspect(decoded_item, label: "Decoded Item in 18D=============")

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-9MSCH18D9M002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" =>
        "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{
          "Code" => "1204_00293",
          # Convert to string
          "Value" => "#{length(item)}",
          "_dataType" => "NUMERIC"
        }
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 418,
          "_areaName" => "BANK BRANCH AND AGENCIES LISTING",
          "DynamicItems" => map_branch_data(item)
        }
      ]
    }
  end

  def perform(%{end_date: end_date, schedule_18d: schedule_18d}) do
    case Poison.decode(schedule_18d) do
      {:ok, decoded} ->
        Logger.info("Decoded Schedule 18D Data: #{inspect(decoded)}")
        perform(decoded, end_date)

      {:error, error} ->
        Logger.error("Error decoding Schedule 18D: #{inspect(error)}")
        perform([], end_date)
    end
  end

  def perform(branches, end_date) when is_list(branches) do
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-9MSCH18D9M002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" =>
        "#{Timex.beginning_of_month(end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{
          "Code" => "1204_00293",
          "Value" => "#{length(branches)}",
          "_dataType" => "NUMERIC"
        }
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 418,
          "_areaName" => "BANK BRANCH AND AGENCIES LISTING",
          "DynamicItems" => map_branch_data(branches)
        }
      ]
    }
  end

  # Fallback for any other case
  def perform(item) do
    Logger.error("Invalid input for Schedule 18D: #{inspect(item)}")

    %{
      "error" => "Invalid input structure",
      "ReturnKey" => "ZM-9MSCH18D9M002",
      "InstCode" => "",
      "ReturnItemsList" => []
    }
  end

  defp map_branch_data(branches) do
    Enum.flat_map(Enum.with_index(branches), fn {branch, index} ->
      index = index + 1

      [
        %{"Code" => "#{index}.1", "Value" => format_value(branch["name"]), "_dataType" => "TEXT"},
        %{
          "Code" => "#{index}.2",
          "Value" => format_value(branch["phy_addr"]),
          "_dataType" => "TEXT"
        },
        %{
          "Code" => "#{index}.3",
          "Value" => format_value(branch["post_addr"]),
          "_dataType" => "TEXT"
        },
        %{"Code" => "#{index}.4", "Value" => format_value(branch["town"]), "_dataType" => "TEXT"},
        %{
          "Code" => "#{index}.5",
          "Value" => format_value(branch["province"]),
          "_dataType" => "TEXT"
        },
        %{
          "Code" => "#{index}.6",
          "Value" => format_value(branch["manager_name"]),
          "_dataType" => "TEXT"
        },
        %{
          "Code" => "#{index}.7",
          "Value" => format_value(branch["phone"]),
          "_dataType" => "TEXT"
        },
        %{"Code" => "#{index}.8", "Value" => format_value(branch["code"]), "_dataType" => "TEXT"},
        %{
          "Code" => "#{index}.9",
          "Value" => format_value(branch["email"]),
          "_dataType" => "TEXT"
        },
        %{
          "Code" => "#{index}.10",
          "Value" => format_value(branch["number"]),
          "_dataType" => "TEXT"
        }
      ]
    end)
  end

  def map_data(decoded_item) do
    # Handle case when decoded_item is a map with a "list" key containing branch data
    if is_map(decoded_item) && Map.has_key?(decoded_item, "list") do
      branches = Map.get(decoded_item, "list", [])

      Enum.flat_map(Enum.with_index(branches), fn {branch, index} ->
        index = index + 1

        [
          %{
            "Code" => "#{index}.1",
            "Value" => "#{branch["name"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.2",
            "Value" => "#{branch["phy_addr"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.3",
            "Value" => "#{branch["post_addr"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.4",
            "Value" => "#{branch["town"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.5",
            "Value" => "#{branch["province"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.6",
            "Value" => "#{branch["manager_name"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.7",
            "Value" => "#{branch["phone"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.8",
            "Value" => "#{branch["code"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.9",
            "Value" => "#{branch["email"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.10",
            "Value" => "#{branch["website"] || "N/A"}",
            "_dataType" => "TEXT"
          }
        ]
      end)
    else
      # If decoded_item is a single branch or has a different structure
      [
        %{"Code" => "1.1", "Value" => "#{decoded_item["name"] || "N/A"}", "_dataType" => "TEXT"},
        %{
          "Code" => "1.2",
          "Value" => "#{decoded_item["phy_addr"] || "N/A"}",
          "_dataType" => "TEXT"
        },
        %{
          "Code" => "1.3",
          "Value" => "#{decoded_item["post_addr"] || "N/A"}",
          "_dataType" => "TEXT"
        },
        %{"Code" => "1.4", "Value" => "#{decoded_item["town"] || "N/A"}", "_dataType" => "TEXT"},
        %{
          "Code" => "1.5",
          "Value" => "#{decoded_item["province"] || "N/A"}",
          "_dataType" => "TEXT"
        },
        %{
          "Code" => "1.6",
          "Value" => "#{decoded_item["manager_name"] || "N/A"}",
          "_dataType" => "TEXT"
        },
        %{"Code" => "1.7", "Value" => "#{decoded_item["phone"] || "N/A"}", "_dataType" => "TEXT"},
        %{"Code" => "1.8", "Value" => "#{decoded_item["code"] || "N/A"}", "_dataType" => "TEXT"},
        %{"Code" => "1.9", "Value" => "#{decoded_item["email"] || "N/A"}", "_dataType" => "TEXT"},
        %{
          "Code" => "1.10",
          "Value" => "#{decoded_item["website"] || decoded_item["J14"] || "N/A"}",
          "_dataType" => "TEXT"
        }
      ]
    end
  end

  # Helper functions for number conversion
  def convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    value = coef * :math.pow(10, exp) * sign
    Decimal.from_float(value)
  end

  def convert_to_number(value) when is_binary(value) do
    case Integer.parse(value) do
      {int_value, ""} -> Decimal.new(int_value)
      _ -> Decimal.new(String.to_float(value))
    end
  rescue
    # Handle any parsing errors
    _ -> Decimal.new(0)
  end

  def convert_to_number(_), do: Decimal.new(0)

  # Add helper function for safer map access
  defp safe_get(map, key, default \\ nil) do
    Map.get(map, "#{key}", default)
  end

  # Add this helper function for safer value formatting
  defp format_value(nil), do: "N/A"
  defp format_value(value) when is_binary(value), do: value
  defp format_value(value), do: "#{value}"
end
