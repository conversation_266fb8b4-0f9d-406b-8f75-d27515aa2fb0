defmodule MisReportsWeb.QuarterlyLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  require Logger
  alias MisReports.Utilities
  alias Utilities.QuarterlyPublicationExport
  alias MisReports.Workers.Quartely.StatementOfLiquidityPosition
  alias MisReportsWeb.UserController
  alias MisReports.Repo
  alias MisReportsWeb.LiveHelpers

  @valid_start_months [1, 4, 7, 10] # January, April, July, October

  @impl true
  def mount(_params, _session, socket) do
    assigns = [
      report_type: "",
      loader: false,
      start_date: nil,
      end_date: nil,
      date_error: nil,
      is_data: %{},
      loader: false,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000],
      # Add missing assigns that publication_list expects
      report_items: [],
      page_num: 1,
      total_pages: 1,
      table_info: %{start_index: 0, end_index: 0, total_entries: 0},
      table_pages: []
    ]
    {:ok, assign(socket, assigns)}
  end

  @impl true
def handle_params(params, _url, socket) do
  {:noreply, apply_action(socket, socket.assigns.live_action, params)}
end

defp apply_action(socket, :index, _params) do

    assign(socket, report_type: "")
  end

  defp apply_action(socket, :publication_list, _params) do
    socket
    |> assign(report_type: "quarterly_list")
    |> publication_list()
  end


  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> publication_list()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> publication_list()}
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> publication_list()}
  end

  defp publication_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          MisReports.Utilities.publication_list(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, report_items: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(report_type: "quarterly_list")
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  @impl true
  def handle_event("validate_date", %{"report" => %{"start_date" => start_date}}, socket) do
    case validate_quarter_start(start_date) do
      {:ok, end_date} ->
        {:noreply, assign(socket, start_date: start_date, end_date: end_date, date_error: nil)}

      {:error, message} ->
        {:noreply, assign(socket, date_error: message, start_date: start_date, end_date: nil)}
    end
  end

  @impl true
  def handle_event("filter-report", %{"report" => params}, socket) do
    case validate_quarter_start(params["start_date"]) do
      {:ok, _end_date} ->
        IO.inspect(params["end_date"], label: "report date======")
        month = String.replace(params["end_date"], "-", "") |> String.slice(0..5)
        entries = MisReports.SourceData.gbm_by_month(month)
        usd_rate = Utilities.get_exchange_rate_by_date_and_code(params["end_date"], "USD")[:exchange_rate_lcy] || "1"
        adjustments = Utilities.get_adjustments(params["end_date"])
        report_type = params["schedule"]
        settings = MisReports.Utilities.get_comapany_settings_params
        quarter_ended_IS = MisReports.Workers.Quartely.IncomeStatementTrend.generate_display(params["start_date"], params["end_date"])

        normalized_data = %{
          quarterly_totals_by_key: quarter_ended_IS[:quarterly_totals_by_key] || %{},
          sum_quarterly_totals_by_keys: quarter_ended_IS[:sum_quarterly_totals_by_keys] || %{},
          ytd_values: quarter_ended_IS[:ytd_values] || %{}
        }

        socket =
          socket
          |> assign(page: %{prev: "Quarterly", current: "Quarterly"})
          |> assign(entries: entries)
          |> assign(filter_params: params)
          |> assign(settings: settings)
          |> assign(adjustments: adjustments)
          |> assign(usd_rate: usd_rate)
          |> assign(is_data: normalized_data)

        handle_event("view_page", %{"value" => "#{report_type}"}, socket)

      {:error, message} ->
        {:noreply, put_flash(socket, :error, message)}
    end
  end

  def handle_event("save-report", _params, socket) do
    send(self(), {:save_publication})
    assigns = [loader: true]
    {:noreply, assign(socket, assigns)}
  end

  def handle_info({:save_publication}, socket) do
    IO.inspect("====================== SAVING DATA =============================")
    report_date = socket.assigns.filter_params["end_date"]
    start_date = socket.assigns.filter_params["start_date"]
    end_date = socket.assigns.filter_params["end_date"]
    month = String.replace(report_date, "-", "") |> String.slice(4..5)
    year = String.replace(report_date, "-", "") |> String.slice(0..3)
    audit_msg = "Generated New quarterly publication report for Quarter Ending Month: #{month} and Year: #{year}"
    user = socket.assigns.current_user
    socket = assign(socket, [loader: false])

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:report, QuarterlyPublicationExport.changeset(%QuarterlyPublicationExport{maker_id: user.id}, %{report_date: report_date, start_date: start_date, end_date: end_date, uuid: Ecto.UUID.generate()}))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{report: _report, audit_log: _user_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Quarterly publication generation successful.")}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Quarterly report Failed to load")}
    end
  end

  # Validate quarter start date and calculate end date
  defp validate_quarter_start(date_str) when is_binary(date_str) do
    case Timex.parse(date_str, "{YYYY}-{0M}-{0D}") do
      {:ok, date} ->
        month = date.month

        if month in @valid_start_months do
          # Calculate the end of the quarter
          end_date = date
            |> Timex.shift(months: 2)
            |> Timex.end_of_month()
            |> Timex.format!("{YYYY}-{0M}-{0D}")

          {:ok, end_date}
        else
          {:error, "Please select January, April, July, or October as the start month"}
        end

      {:error, _} ->
        {:error, "Invalid date format"}
    end
  end

  defp validate_quarter_start(_), do: {:error, "Invalid date"}

  def handle_event("view_page", %{"value" => schedule_type}, socket) do
    data = socket.assigns.entries
    usd_rate = socket.assigns.usd_rate
    adjustments = socket.assigns.adjustments
    filter_params = socket.assigns.filter_params

    new_prudential?(nil, schedule_type, data, filter_params, usd_rate, adjustments)

    assigns = [
      report_type: schedule_type,
      loader: true,
      filter_params: filter_params,
      data: nil,
      header: []
    ]
    {:noreply, assign(socket, assigns)}
  end

  def clean_data(entries) do
    Enum.map(entries, fn e ->
      case String.length(e.sap_gl_acc_no) > 6 do
        true -> Map.put(e, :sap_gl_acc_no, String.slice(e.sap_gl_acc_no, 4..-1))
        false -> e
      end
    end)
  end

  @impl true
  def handle_info({ref, {:load_schedule, data}}, socket) do
    Process.demonitor(ref, [:flush])

    combined_data =
      data
      |> Map.merge(data.blc_sheet, fn _k, _v1, v2 -> v2 end)
      |> Map.merge(data.liquid_position, fn _k, _v1, v2 -> v2 end)
      |> Map.merge(data.sh15, fn _k, _v1, v2 -> v2 end)

    assigns = [
      report_type: data.schedule_type,
      loader: false,
      filter_params: data.filter_params,
      data: combined_data
    ]

    {:noreply, assign(socket, assigns)}
  end

  @impl true
  def handle_info({:DOWN, _ref, :process, _pid, _reason}, socket) do
    {:noreply, socket}
  end

  defp new_prudential?(nil, schedule_type, data, filter_params, usd_rate, adjustments) do
    Task.async(fn ->
      case schedule_type do
        "quarterly" ->
          # Generate current income
          income = MisReports.Workers.IS.generate_display(data, filter_params["end_date"], adjustments)

          # Calculate previous quarter end date and generate prev_income
          start_date = filter_params["end_date"] |> Date.from_iso8601!()

          prev_date = Timex.shift(start_date, months: -1) |> Timex.end_of_month()

          date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")

          month = String.replace(filter_params["end_date"], "-", "") |> String.slice(0..5)
          Logger.debug("Usd Rate: #{inspect(usd_rate)}")
          Logger.debug("End Date: #{inspect(filter_params["end_date"])}")
          Logger.debug("Start Date: #{inspect(filter_params["start_date"])}")
          Logger.debug("Parsed Start Date: #{inspect(start_date)}")
          Logger.debug("Month slice: #{inspect(month)}")
          Logger.debug("Previous Date: #{inspect(prev_date)}")
          Logger.debug("Date String: #{inspect(date_string)}")

          prev_income = MisReports.Workers.IS.generate_display(data, date_string, adjustments)

          # Pass both income and prev_income to Sh13
          shd13 = MisReports.Workers.Sh13.generate_display(
            income,
            prev_income,
            filter_params["end_date"]
          )

          blc_sheet = MisReports.Workers.BalanceSheet.generate_display(
            shd13,
            data,
            filter_params["end_date"],
            filter_params["end_date"],
            usd_rate,
            adjustments
          )

          IO.inspect(blc_sheet, label: "BSHEET==============", limit: :infinity)

          sh15 = MisReports.Workers.Sh15.generate_display(
            shd13,
            blc_sheet,
            filter_params["end_date"],
            adjustments
          )

          liquid_position = StatementOfLiquidityPosition.generate_display(
            data,
            shd13,
            filter_params["end_date"],
            adjustments,
            usd_rate
          )

          {:load_schedule, %{
            income: income,
            shd13: shd13,
            liquid_position: liquid_position,
            blc_sheet: blc_sheet,
            sh15: sh15,
            filter_params: filter_params,
            schedule_type: schedule_type
          }}
      end
    end)
  end
end
