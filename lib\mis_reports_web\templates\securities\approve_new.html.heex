
<style>
    .ex1{
       transition: all 1.5s;
       animation: animation-name 0.5s linear;
    }
    
    @keyframes animation-name{
       from{
       transform: translateX(-100%);
       height: 0px;
       }
    }
    </style>
    
    <.form :let={f} for={@changeset} as={:securities} id="securities-form" phx-submit="save" phx-target={@myself}>
        <div id="alert-2" class="divide-y divide-gray-200 overflow-hidden rounded-lg ex1 bg-white shadow mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
           <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6 ">
              <div class="-ml-4 -mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
                 <div class="ml-4 mt-4">
                    
                 </div>
                 <div class="ml-4 mt-4 flex flex-shrink-0">
                 </div>
              </div>
           </div>
           
             <div class="sm:col-span-5 grid-cols-4 sm:grid-cols-3">
              <div class="space-y-12">
                 <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-5 grid grid-cols-4 gap-x-3  sm:grid-cols-3">  
                       <div class="sm:col-span-1">
                          <label for="last-name" class="block text-sm  leading-6 text-gray-900">Security Type</label>
                          <%= select(
                            f,
                            :security_type,
                            [{"Treasury Bill", "Treasury Bills"},{"Government Bonds", "Government Bonds"},{"Government other Bonds", "Government Other Bonds"},{"Other securities", "Other securities"} ],
                            selected: input_value(f, :security_type),
                            disabled: "true",
                            class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            ) %> 
                       </div>
 
                       <div class="sm:col-span-1">
                         <label for="last-name" class="block text-sm  leading-6 text-gray-900">Counterparty Name</label>
                         <div class="mt-2">
                            <%= text_input(
                               f,
                               :counter_name,
                               autocomplete: "",
                               placeholder: "Counterparty Name",
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               required: "true",
                               disabled: "true"
                             ) %>   
                 
                         </div>
                      </div>
    
                       <div class="sm:col-span-1">
                         <label for="last-name" class="block text-sm  leading-6 text-gray-900">Purpose of Pledge</label>    
                        <%= select(
                            f,
                            :purpose_pledge,
                            [{"ZECHL Collateral", "ZECHL Collateral"},{"Interbank", "Interbank"},{"Overnight Lending Facility", "Overnight Lending Facility"},{"Deposits", "Deposits"},{"Emergency Liquidity Assist", "Emergency Liquidity Assist"},{"TMTRF", "TMTRF"},{"Other", "Other"} ],
                            selected: input_value(f, :purpose_pledge),
                            disabled: "true",
                            class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            ) %>
                      </div>
                       <div class="sm:col-span-1">
                          <label for="last-name" class="block text-sm  leading-6 text-gray-900">Date Pledged</label>
                          <div class="mt-2">
                            <%= date_input(
                               f,
                               :pledge_date,
                               autocomplete: "",
                               placeholder: "Date Pledged",
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               required: "true",
                               disabled: "true"
                             ) %> 
                          </div>
                       </div>
                       <div class="sm:col-span-1">
                          <label for="first-name" class="block text-sm  leading-6 text-gray-900">Expiry Date</label>
                          <div class="mt-2">
                           
                            <%= date_input(
                               f,
                               :expiry_date,
                               autocomplete: "",
                               placeholder: "Expiry Date",
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               required: "true",
                               disabled: "true"
                             ) %> 
                          </div>  
                       </div> 
                       
                       <div class="sm:col-span-1">
                        <label for="first-name" class="block text-sm  leading-6 text-gray-900">Currency</label>
                        <%= select(
                            f,
                            :currency,
                            [{"ZMW", "ZMW"},{"USD", "USD"},{"GBP", "GBP"},{"ZAR", "ZAR"},{"EUR", "EUR"},{"NGN", "NGN"},{"DKK", "DKK"},{"ITL", "ITL"},{"CNY", "CNY"},{"JPY", "JPY"},{"Other", "Other"} ],
                            selected: input_value(f, :currency),
                            disabled: "true",
                            class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            ) %>
                     </div> 
                     
                     <div class="sm:col-span-1">
                      <label for="first-name" class="block text-sm  leading-6 text-gray-900">cost of security</label>
                      <div class="mt-2">
                         <%= number_input(
                               f,
                               :cost_security,
                               autocomplete: "",
                               placeholder: "",
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               required: "true",
                               disabled: "true"
                             ) %><%= error_tag(f, :cost_security) %>
                      </div>  
                   </div>
                   
                   <div class="sm:col-span-1">
                    <label for="first-name" class="block text-sm  leading-6 text-gray-900">Face/ par Value of Security Pledged</label>
                    <div class="mt-2">
                      
                      <%= number_input(
                               f,
                               :security_pledge,
                               autocomplete: "",
                               placeholder: "Face/ par Value of Security Pledged",
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               required: "true",
                               disabled: "true"   
                             ) %> 
                    </div>  
                 </div>
    
    
                 <div class="sm:col-span-1">
                  <label for="first-name" class="block text-sm  leading-6 text-gray-900">Total amount of Obligation Secured</label>
                  <div class="mt-2">
                   
                   <%= number_input(
                      f,
                      :total_secured,
                      autocomplete: "",
                      placeholder: "Total amount of Obligation Secured",
                      class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                      required: "true",
                      disabled: "true"
                    ) %>
                  </div>  
               </div>
    
               <div class="sm:col-span-1">
                <label for="first-name" class="block text-sm  leading-6 tex    t-gray-900">Report Date</label>
                <div class="mt-2">
                   
                   <%= date_input(
                      f,
                      :report_date,
                      autocomplete: "",
                      placeholder: "Report Date",
                      class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                      required: "true",
                      disabled: "true"
                    ) %>
                </div>  
             </div>
    
                </div>
               </div>
             </div>
             <div class="mt-6 flex items-center justify-end gap-x-6">
                <button 
                  type="submit" 
                  phx-click="save"
                  phx-target={@myself}
                  phx-value-action="97"
                  class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600">
                  Approve
                </button>
        
                <button 
                  type="submit"
                  phx-click="save"
                  phx-target={@myself}
                  phx-value-action="96"
                  class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
                  Reject
                </button>
              </div>
              
           </div>
        </div>
        </.form>
        <.confirm_modal /> <.info_notification /> <.error_notification />
 
 