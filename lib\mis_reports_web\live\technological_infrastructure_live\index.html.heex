<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
    <div class="mt-5 font-semibold text-xl">Technological Infrastructure</div>
    <%= if @live_action == :new do %>
      <div class="text-sm">Create Technological Infrastructure</div>
    <% end %>
    <%= if @live_action == :edit do %>
      <div class="text-sm">Edit Technological Infrastructure</div>
    <% end %>
    <%= if @live_action == :index do %>
      <div class="text-sm">Technological Infrastructure</div>
    <% end %><br>
    
    <.info :if={live_flash(@flash, :info)} flash={@flash} />
    <.error :if={live_flash(@flash, :error)} flash={@flash} />
    
    <%!-- <%= if @live_action in [:new, :edit] do %>
      <.live_component module={TechnologicalInfrastructureLive.TechnologicalInfrastructureComponent} id="new-benefits" values={@values} technological_infrastructure={@technological_infrastructure} action={@live_action} current_user={@current_user} />
    <% end %> --%>
    <.live_component 
      :if={@live_action in [:new, :edit, :update_status]} 
      module={TechnologicalInfrastructureLive.TechnologicalInfrastructureComponent} 
      id="new-tech-infra" 
      values={@values} 
      technological_infrastructure={@technological_infrastructure} 
      current_user={@current_user}
      process_id={@process_id}
      reference={@reference}
      step_id={@step_id}
      action={@live_action}
    />
    <%= if @live_action == :index do %>
        <%= Phoenix.View.render(MisReportsWeb.TechnologicalInfrastructureView, "technological_infrastructures.html", assigns) %>
    <% end %>
    </div>
    
    <.confirm_modal />
    
    
    <.info_notification />
    
    
    <.error_notification />
    
    