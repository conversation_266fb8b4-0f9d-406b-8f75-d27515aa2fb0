defmodule MisReportsWeb.RepossessedPropertiesLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.{Utilities, Repo}
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Utilities
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.UserLiveAuth
  alias MisReports.Utilities.RepossedProp
  alias MisReportsWeb.RepossessedPropertiesController
  import MisReportsWeb.UserLive.Index, only: [put_conn_user: 1]

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000],
       process_id: nil,
       reference: nil,
       step_id: nil,
       action: nil
     )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do
      {:noreply,
       socket
       |> assign(:process_id, params["process_id"])
       |> assign(:reference, params["reference"])
       |> assign(:step_id, params["step_id"])
       |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :new, _params) do
    changeset = Utilities.change_RepossedProp(%RepossedProp{})

    socket
    |> assign(:changeset, changeset)
    |> assign(:repossed_prop, %RepossedProp{})
    |> assign(:action, :new)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    repossed_prop = Utilities.get_repossed_prop!(id)
    changeset = Utilities.change_RepossedProp(repossed_prop)

    assign(socket, :repossed_prop, repossed_prop)
    |> assign(:changeset, changeset)
    |> assign(:action, :edit)
    # Add a page assign to handle conditional rendering in the template
    |> assign(:page, :edit)
  end

  defp apply_action(socket, :index, _params), do: list_tbl_repossessed_properties(socket)

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        repossed_prop = Utilities.get_repossed_prop_by_reference!(reference)
        changeset = Utilities.change_RepossedProp(repossed_prop)
        IO.inspect(repossed_prop, label: "repossed_prop")

        socket
        |> assign(:repossed_prop, repossed_prop)
        |> assign(:changeset, changeset)
        |> assign(:reference, reference)
    end
  end

  defp list_tbl_repossessed_properties(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Utilities.list_tbl_repossessed_properties(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  @impl true
  def handle_event("validate", %{"repossed_prop" => repossed_prop_params}, socket) do
    changeset =
      socket.assigns.repossed_prop
      |> RepossedProp.changeset(repossed_prop_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_tbl_repossessed_properties()}
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_tbl_repossessed_properties()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_tbl_repossessed_properties()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:report_date, :maker_id, :status], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)

    {:noreply,
     socket
     |> assign(sort_by: sort_by)
     |> list_tbl_repossessed_properties()}
  end

  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  # @impl true
  # def handle_event("save", %{"repossed_prop" => repossed_prop_params}, socket) do
  #   save_repossed_prop(socket, socket.assigns.action, repossed_prop_params)
  # end

  # defp save_repossed_prop(socket, :new, params) do

  #   put_conn_user(socket)
  #   |> RepossessedPropertiesController.create(%{"repossed_prop" => params})
  #   |> case do
  #     {:ok, _repossed_prop} ->
  #       {:noreply,
  #        socket
  #        |> put_flash(:info, "Repossessed Property created successfully")
  #        |> push_redirect(to: Routes.repossessed_properties_index_path(socket, :new))}
  #       {:error, %Ecto.Changeset{} = changeset} ->
  #       {:noreply, assign(socket, changeset: changeset)}
  #   end
  # end

  # defp save_repossed_prop(socket, :edit, params) do
  #   repossed_prop = socket.assigns.repossed_prop
  #   socket
  #   |> handle_update(params, repossed_prop)
  #   |> case do
  #     {:ok, repossed_prop} ->
  #       {:noreply,
  #         socket
  #         |> put_flash(:info, "Repossessed Property updated successfully")
  #         |> push_redirect(to: Routes.repossessed_properties_index_path(socket, :edit, repossed_prop))}

  #     {:error, %Ecto.Changeset{} = changeset} ->
  #       {:noreply, assign(socket, changeset: changeset)}
  #   end
  # end

  # def handle_update(socket, params, repossed_prop) do
  #   audit_msg = "Updated new Repossessed Property "

  #   Ecto.Multi.new()
  #   |> Ecto.Multi.update(:repossed_prop, RepossedProp.changeset(repossed_prop, params))
  #   |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
  #   |> Repo.transaction()
  #   |> case do
  #     {:ok, %{repossed_prop: repossed_prop, audit_log: _user_log}} ->
  #       {:ok, repossed_prop}

  #     {:error, _failed_operation, failed_value, _changes_so_far} ->
  #       {:error, failed_value}
  #   end
  # end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    repossed_prop = Utilities.get_repossed_prop!(id)
    audit_msg = "changed status for reposssessed properties to: #{status}"

    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      RepossedProp.changeset(repossed_prop, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _loan_sector, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Operation Succesfull!")
         |> push_redirect(to: Routes.repossessed_properties_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    repossed_prop = Utilities.get_repossed_prop!(id)
    audit_msg = "Deleted Employee Benefit:"
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_repossessed_prop, repossed_prop)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_repossessed_prop: _loan_sector, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, " Deleted successfully!")
         |> push_redirect(to: Routes.repossessed_properties_index_path(socket, :index))}

      {:error, failed_value} ->
        {:error, failed_value}
    end
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"repossessed_properties", "new"}

      act when act in ~w(edit)a ->
        {"repossessed_properties", "edit"}

      act when act in ~w(update_status)a ->
        {"repossessed_properties", "update_status"}

      act when act in ~w(delete)a ->
        {"repossessed_properties", "delete"}

      act when act in ~w(index)a ->
        {"repossessed_properties", "index"}

      _ ->
        {"repossessed_properties", "unknown"}
    end
  end
end
