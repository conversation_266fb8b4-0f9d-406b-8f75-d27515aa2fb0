<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
    <div class="mt-5 font-semibold text-xl">Regulatory Capital</div>
    <%= if @live_action == :new do %>
      <div class="text-sm">New Regulatory Capital</div>
    <% end %>
    <%= if @live_action == :edit do %>
      <div class="text-sm">Edit Regulatory Capital</div>
    <% end %>
    <%= if @live_action == :index do %>
      <div class="text-sm">View Regulatory Capital</div>
    <% end %><br>
    
    <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
    <.error :if={live_flash(@flash, :error)} flash={@flash} />

     <%= if @live_action == :update_status do %>
      <.live_component 
        module={MisReportsWeb.RegulatoryCapitalLive.RegulatoryCapitalComponent} 
        id="new-regulatory-capital" 
        current_user={@current_user} 
        regulatory_capital={@regulatory_capital}
        process_id={@process_id}
        reference={@reference}
        step_id={@step_id}
        action={@live_action}
      />
    <% end %>
    
    <%= if @live_action == :index do %>
        <%= Phoenix.View.render(MisReportsWeb.RegulatoryCapitalView, "regulatory_capital_list.html", assigns) %>
    <% end %>

    <.live_component :if={@live_action in [:new, :edit]} module={ MisReportsWeb.RegulatoryCapitalLive.RegulatoryCapitalComponent} id="new-regulatory-capital" current_user={@current_user} regulatory_capital={@regulatory_capital} action={@live_action}
        process_id={@process_id}
        reference={@reference}
        step_id={@step_id}
        action={@live_action} />
</div>
        
<.confirm_modal />

<.info_notification />

<.error_notification />
