<.form :let={f} for={@changeset}  as={:off_blc_sheet} id="off_blc_sheet" phx-submit="save" phx-change="validate" phx-target={@myself}>
    <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
      <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
        <div class="-ml-4 -mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
          <div class="ml-4 mt-4">
            <div class="flex items-center">
              <div class="ml-4">
                <h3  class="text-base font-semibold leading-6 text-gray-900">Off Balance sheet </h3>
                <p class="text-sm text-gray-500">
                  <a href="#">off Balance sheet</a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <div class="px-4 py-5 sm:p-6">
        <div class="space-y-12">
          <div class="border-b border-gray-900/10 pb-12">
            <div class="mt-8 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
              <div class="sm:col-span-3">
                <label for="import_letters_credit" class="block text-sm font-medium leading-6 text-gray-900">performerce bond</label>
                <div class="mt-2">
                  <%= number_input(
                    f,
                    :performance_bonds,
                    autocomplete: "sight import letters of credit",
                    placeholder: "sight import letters of credit",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %> 
                </div>
              </div>
  
              
              <div class="sm:col-span-3">
                <label for="import_letters_credit" class="block text-sm font-medium leading-6 text-gray-900">sight import letters of credit</label>
                <div class="mt-2">
                  <%= number_input(
                    f,
                    :import_letters_credit,
                    autocomplete: "sight import letters of credit",
                    placeholder: "sight import letters of credit",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"    
                  ) %> 
                </div>
              </div>
              <div class="sm:col-span-3">
                <label for="trade_securities" class="block text-sm font-medium leading-6 text-gray-900">securities purchased under resale agreement</label>
                <div class="mt-2">
                  <%= number_input(
                    f,
                    :resale_agreement,
                    autocomplete: "securities purchased under resale agreement",
                    placeholder: "securities purchased under resale agreement",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %>
                </div>
              </div>
  
              <div class="sm:col-span-3">
                <label for="trade_securities" class="block text-sm font-medium leading-6 text-gray-900">guarantees for loans, trade and securities</label>
                <div class="mt-2">
                  <%= number_input(
                    f,
                    :trade_securities,
                    autocomplete: "guarantees for loans, trade and securities",
                    placeholder: "guarantees for loans, trade and securities",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %> 
                </div>
              </div>
  
              <div class="sm:col-span-3">
                <label for="contingent_liabilities" class="block text-sm font-medium leading-6 text-gray-900">other contingent liabilities</label>
                <div class="mt-2">
                  <%= number_input(
                    f,
                    :contingent_liabilities,
                    autocomplete: "other contingent liabilities",
                    placeholder: "other contingent liabilities",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %> 
                </div>
              </div>
  
  
              <div class="sm:col-span-3">
                <label for="report_dt" class="block text-sm font-medium leading-6 text-gray-900">Report Date</label>
                <div class="mt-2">
                  <%= date_input(
                    f,
                    :report_dt,
                    autocomplete: "Report Date",
                    placeholder: "Report Date",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %>
                </div>
              </div>
  
            </div>
          </div>
        </div>
  
        <div class="mt-6 flex items-center justify-end gap-x-6">
            <button 
              type="submit" 
              phx-click="save"
              phx-target={@myself}
              phx-value-action="97"
              class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600">
              Approve
            </button>
    
            <button 
              type="submit"
              phx-click="save"
              phx-target={@myself}
              phx-value-action="96"
              class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
              Reject
            </button>
          </div>
          
      </div>
    </div>
  </.form>
  
  