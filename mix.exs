defmodule MisReports.MixProject do
  use Mix.Project

  def project do
    [
      app: :mis_reports,
      version: "0.1.0",
      elixir: "~> 1.12",
      elixirc_paths: elixirc_paths(Mix.env()),
      compilers: Mix.compilers(),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps()
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {MisReports.Application, []},
      extra_applications: [:logger, :runtime_tools]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      {:phoenix, "~> 1.6.7"},
      {:phoenix_ecto, "~> 4.4"},
      {:ecto_sql, "~> 3.6"},
      {:tds, "~> 2.2"},
      {:phoenix_html, "~> 3.0", override: true},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 0.18.2"},
      {:floki, ">= 0.30.0", only: :test},
      {:esbuild, "~> 0.4", runtime: Mix.env() == :dev},
      {:telemetry_metrics, "~> 0.6"},
      {:telemetry_poller, "~> 1.0"},
      {:gettext, "~> 0.18"},
      # {:pdf_generator, "~> 0.6.2"},
      {:decimal, "~> 2.0.0", override: true},
      {:jason, "~> 1.2"},
      {:plug_cowboy, "~> 2.5"},
      {:httpoison, "~> 0.13.0"},
      {:poison, "~> 3.1", override: true},
      {:atomic_map, "~> 0.8"},
      {:scrivener, "~> 2.0"},
      {:scrivener_ecto, "~> 2.7", override: true},
      {:timex, "~> 3.6"},
      {:calendar, "~> 0.17.0"},
      {:mime, "~> 2.0", override: true},
      {:bamboo, "~> 1.3"},
      {:bamboo_smtp, "~> 2.1.0"},
      {:number, "~> 0.5.7"},
      {:csv, "~> 2.0.0"},
      {:quantum, "~> 3.4"},
      {:logger_file_backend, "~> 0.0.10"},
      {:elixlsx, "~> 0.4.2"},
      {:pipe_to, "~> 0.2"},
      {:ex_crypto, "~> 0.10.0"},
      {:credo, "~> 1.5", only: [:dev, :test], runtime: false},
      {:ex_phone_number, "~> 0.1"},
      {:phone, "~> 0.5.2"},
      {:sleeplocks, "~> 1.0"},
      {:endon, "~> 1.0"},
      {:cachex, "~> 3.3"},
      {:phoenix_html_sanitizer, "~> 1.0.0"},
      {:html_sanitize_ex, "~> 1.4", override: true},
      {:html_entities, "~> 0.5.2"},
      {:bbmustache, "~> 1.11"},
      # {:pdf_generator, ">=0.6.2"},
      {:flow, "~> 1.1"},
      {:date_time_parser, "~> 1.1"},
      {:nimble_csv, "~> 1.2"},
      {:one_time_pass_ecto, "~> 1.0"},
      {:params, "~> 2.2"},
      {:password_validator, "~> 0.4.1"},
      {:passgen, "~> 0.1.1"},
      {:xlsxir, "~> 1.6.2"},
      {:excelizer, "~> 0.1.7"},
      {:tailwind, "~> 0.1", runtime: Mix.env() == :dev},
      {:phoenix_multi_select, "~> 0.0"}  # Removed trailing comma


    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.deploy": ["tailwind default --minify", "esbuild default --minify", "phx.digest"]
    ]
  end
end
