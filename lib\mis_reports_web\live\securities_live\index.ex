defmodule MisReportsWeb.SecuritiesLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.{Repo}
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Utilities
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.UserLiveAuth
  alias MisReports.Utilities.Securities

  @impl true
  def mount(_params, _session, socket) do
    changeset = Utilities.change_securities(%Securities{})
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       currency_code: "",
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000],
       process_id: nil,
       reference: nil,
       step_id: nil
     )
     |> assign(:changeset, %{changeset | errors: %{}})
    }
  end




  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do

      socket =
        socket
        |> assign(:process_id, params["process_id"])
        |> assign(:reference, params["reference"])
        |> assign(:step_id, params["step_id"])

       {:noreply,
         socket
         |> apply_action(socket.assigns.live_action, params)}
     else
       UserLiveAuth.unauthorized(socket)
     end
  end


  defp apply_action(socket, :new, _params) do
    # assign(socket, :securities, %Securities{})

    socket
    |> assign(:securities, %Securities{})
    |> assign(:action, :new)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    securities = Utilities.get_securities!(id)
    rate = securities.currency #|| "1"  Use a suitable default value
    changeset = Utilities.change_securities(securities)

    socket
    |> assign(:securities, securities)
    |> assign(:currency_code, rate)
    |> assign(:changeset, changeset)
    |> assign(:action, :edit)
  end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        # |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        securities = Utilities.get_securities_by_reference!(reference)

        socket
        |> assign(:securities, securities)
        |> assign(:changeset, Utilities.change_securities(securities))
        # Explicitly reassign reference
        |> assign(:reference, reference)
    end
  end

  defp apply_action(socket, :index, _params), do: list_securities_listing(socket)



  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]
    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end


  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_securities_listing()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_securities_listing()}
  end

  @impl true
  def handle_event("select_option", %{"option" => option}, socket) do
    {:noreply, assign(socket, selected_option: option)}
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_securities_listing()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:counter_name, :report_date,  :securities_lcy, :maker_id, :status], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)

    {:noreply,
      socket
      |> assign(sort_by: sort_by)
      |> list_securities_listing()}
  end

  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    securities = Utilities.get_securities!(id)
    audit_msg = "Changed status for currency: \"#{securities.currency_code}\" for the date of \"#{securities.report_date}\"  to: #{status}"
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      Securities.changeset(securities, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _securities, audit_log: _audit_log}} ->
        {:noreply,
        socket
        |> put_flash(:info, "Operation Succesfull!")
        |> push_redirect(
          to: Routes.securities_index_path(socket, :index)
        )}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = traverse_errors(failed_value.errors) |> List.first()

        {:noreply,
        socket
        |> put_flash(:error, reason)
        |> push_redirect(
          to: Routes.securities_index_path(socket, :index)
        )}
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    securities = Utilities.get_securities!(id)
    audit_msg =
      "Deleted Securities: \"#{securities.currency_code}\" for the date of \"#{securities.report_date}\""
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_securities, securities)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_securities: _securities, audit_log: _audit_log}} ->
        {:noreply,
        socket
        |> put_flash(:info, "Deleted successfully!")
        |> push_redirect(
           to: Routes.securities_index_path(socket, :index)
        )}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = traverse_errors(failed_value.errors) |> List.first()

        {:noreply,
        socket
        |> put_flash(:error, reason)
        |> push_redirect(
          to: Routes.securities_index_path(socket, :index)
        )}
    end
  end

  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

  def update_securities(socket, params, securities) do
    # params = prep_changes(benefit_params)
    audit_msg = "Updated securities  Status for the date of \"#{securities.report_date}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(:securities, Securities.changeset(securities, Map.merge(params, %{"status" => "D"})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{securities: securities, audit_log: _user_log}} ->
        {:ok, securities}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp list_securities_listing(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->

          Utilities.list_securities_listing(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"securities", "new"}

      act when act in ~w(edit)a ->
        {"securities", "edit"}

      act when act in ~w(update_status)a ->
        {"securities", "update_status"}

      act when act in ~w(delete)a ->
        {"securities", "delete"}

      act when act in ~w(index)a ->
        {"securities", "index"}

      _ ->
        {"securities", "unknown"}
    end
  end
end
