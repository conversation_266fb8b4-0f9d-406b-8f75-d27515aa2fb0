<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
    <div class="mt-5 font-semibold text-xl">Listing of Encumbered Securities</div>
    <%= if @live_action == :new do %>
      <div class="text-sm">Listing of Encumbered Securities</div>
    <% end %>
    <%= if @live_action == :edit do %>
      <div class="text-sm">Listing of Encumbered Securities</div>
    <% end %>
    <%= if @live_action == :index do %>
      <div class="text-sm">Listing of Encumbered Securities</div>
    <% end %><br>
    
    <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
    <.error :if={live_flash(@flash, :error)} flash={@flash} />

    <%= if @live_action == :update_status do %>   
      <.live_component 
      module={MisReportsWeb.SecuritiesLive.SecuritiesComponent} 
      id="new-securities" 
      current_user={@current_user} 
      securities={@securities} 
      changeset={@changeset}
      process_id={@process_id}
      reference={@reference}
      step_id={@step_id}
      action={@live_action} />

    <% end %>  

    <.live_component 
    :if={@live_action in [:new, :edit]} 
    module={MisReportsWeb.SecuritiesLive.SecuritiesComponent} 
    id="new-securities" 
    current_user={@current_user} 
    securities={@securities} 
    process_id={@process_id}
      reference={@reference}
      step_id={@step_id}
    action={@live_action} />
   
    <%= if @live_action == :index do %>
        <%= Phoenix.View.render(MisReportsWeb.SecuritiesView, "securities_listing.html", assigns) %>
    <% end %>
    
</div>
    
<.confirm_modal />

<.info_notification />

<.error_notification />

