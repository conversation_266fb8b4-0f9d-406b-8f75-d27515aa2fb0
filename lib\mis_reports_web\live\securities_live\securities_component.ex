defmodule MisReportsWeb.SecuritiesLive.SecuritiesComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Utilities, Repo}
  alias MisReports.Utilities.Securities
  alias MisReportsWeb.UserController

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.SecuritiesView, "securities.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.SecuritiesView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{securities: securities} = assigns, socket) do
    changeset = Utilities.change_securities(securities)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"securities" => securities_params}, socket) do
    changeset =
      socket.assigns.securities
      |> Securities.changeset(securities_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  @impl true
  def handle_event("save", %{"securities" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_save(socket, :reject, params)
      "97" -> handle_save(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp handle_save(socket, :new, params) do
    audit_msg = "Created new Securities: \"#{params["security_type"]}\" for counterparty: \"#{params["counter_name"]}\""
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:securities, Securities.changeset(%Securities{maker_id: user.id}, params))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{securities: securities}} ->
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Securities Creation"
             ) do
          {:ok, reference_number} ->
            case Utilities.update_securities(securities, %{reference: reference_number}) do
              {:ok, updated_securities} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "Securities created successfully. Reference: #{reference_number}")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update securities reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Securities created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  defp handle_save(socket, :edit, params) do
    securities = socket.assigns.securities

    socket
    |> handle_update(params, securities)
    |> case do
      {:ok, securities} ->
        {:noreply,
          socket
          |> put_flash(:info, "securities updated successfully")
          |> push_redirect(to: Routes.securities_index_path(socket, :index))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  defp handle_save(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Securities Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Securities rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}
      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject securities: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp handle_save(socket, :approve, params) do
    securities = socket.assigns.securities
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Securities Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      Securities.changeset(securities, %{
        status: "A",
        checker_id: current_user.id,
        checker_date: NaiveDateTime.utc_now()
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_securities}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Securities approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}
          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Securities approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end
      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve securities")
         |> assign(:changeset, %{securities.changeset | errors: failed_value.errors})}
    end
  end

  def handle_update(socket, params, securities) do
    audit_msg = "Updated Securities: \"#{params["security_type"]}\" for counterparty: \"#{params["counter_name"]}\""

    updated_changeset =
      securities
      |> Securities.changeset(params)
      |> Ecto.Changeset.put_change(:status, "D")
      |> Ecto.Changeset.put_change(:checker_id, nil)
      |> Ecto.Changeset.put_change(:maker_id, socket.assigns.current_user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:securities, updated_changeset)
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{securities: securities, audit_log: _user_log}} ->
        {:ok, securities}
      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end
end
