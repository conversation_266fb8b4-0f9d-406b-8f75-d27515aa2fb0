
defmodule MisReportsWeb.RegulatoryCapitalLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.{Repo}
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Prudentials
  alias MisReports.Prudentials.RegulatoryCapital
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.UserLiveAuth

  @impl true
  def mount(params, _session, socket) do
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       currency_code: "",
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000],
      process_id: params["process_id"],
      reference: params["reference"],
      step_id: params["step_id"],
     )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do
        process_id = params["process_id"] || socket.assigns.process_id
    reference = params["reference"] || socket.assigns.reference
    step_id = params["step_id"] || socket.assigns.step_id
      {:noreply,
       socket
       |> assign(:process_id, process_id)
       |> assign(:reference, reference)
       |> assign(:step_id, step_id)
       |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :new, _params) do
    assign(socket, :regulatory_capital, %RegulatoryCapital{})
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    regulatory_capital = Prudentials.get_regulatory_capital!(id)

    socket
    |> assign(:regulatory_capital, regulatory_capital)
  end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        regulatory_capital = Prudentials.get_regulatory_capital_reference!(reference)

        socket
        |> assign(:regulatory_capital, regulatory_capital)
        |> assign(:reference, reference)
    end
  end

  defp apply_action(socket, :index, _params), do: list_regulatory_capital(socket)

  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_regulatory_capital()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_regulatory_capital()}
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_regulatory_capital()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:regulatory_capital, :date, :inserted_at, :status], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)

    {:noreply,
     socket
     |> assign(sort_by: sort_by)
     |> list_regulatory_capital()}
  end

  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

  def audit_log(multi, user_id, audit_msg) do
    Ecto.Multi.run(multi, :audit_log, fn repo, _changes_so_far ->
      MisReports.Audit.UserLog.changeset(%MisReports.Audit.UserLog{}, %{
        user_id: user_id,
        activity: audit_msg
      })
      |> repo.insert()
    end)
  end

  def update_regulatory_capital(socket, params, regulatory_capital) do
    audit_msg = "Updated Regulatory Capital for the Month \"#{regulatory_capital.date}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :regulatory_capital,
      RegulatoryCapital.changeset(regulatory_capital, Map.merge(params, %{"status" => "D"}))
    )
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{regulatory_capital: regulatory_capital, audit_log: _user_log}} ->
        {:ok, regulatory_capital}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    regulatory_capital = Prudentials.get_regulatory_capital!(id)

    audit_msg =
      "Changed status for Regulatory:  for the Month of \"#{regulatory_capital.date}\"  to: #{status}"

    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      RegulatoryCapital.changeset(regulatory_capital, %{
        status: status,
        checker_id: current_user.id
      })
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _exchange_rate, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Operation Succesfull!")
         |> push_redirect(to: Routes.regulatory_capital_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = traverse_errors(failed_value.errors) |> List.first()

        {:noreply,
         socket
         |> put_flash(:error, reason)
         |> push_redirect(to: Routes.regulatory_capital_index_path(socket, :index))}
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    regulatory_capital = Prudentials.get_regulatory_capital!(id)
    audit_msg = "Deleted Regulatory Capital for the Month of \"#{regulatory_capital.date}\""
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_regulatory_capital, regulatory_capital)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_regulatory_capital: _del_regulatory_capital, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Regulatory Capital Deleted successfully!")
         |> push_redirect(to: Routes.regulatory_capital_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = traverse_errors(failed_value.errors) |> List.first()

        {:noreply,
         socket
         |> put_flash(:error, reason)
         |> push_redirect(to: Routes.regulatory_capital_index_path(socket, :index))}
    end
  end

  defp list_regulatory_capital(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Prudentials.list_regulatory_capitals(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"regulatory_capital", "new"}

      act when act in ~w(edit)a ->
        {"regulatory_capital", "edit"}

      act when act in ~w(update_status)a ->
        {"regulatory_capital", "update_status"}

      act when act in ~w(delete)a ->
        {"regulatory_capital", "delete"}

      act when act in ~w(index)a ->
        {"regulatory_capital", "index"}

      _ ->
        {"regulatory_capital", "unknown"}
    end
  end
end
