<.table page_size={@page_size} length_menu={@length_menu} table_info={@table_info} page_num={@page_num} total_pages={@total_pages} table_pages={@table_pages}>
  <:thead>
    <tr>
      <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
        <div class="flex items-center">
         Filename
          <a href="#" phx-click="table_sort" phx-value-sort_by="doc_description" phx-value-sort_dir={reverse_sort_order(@sort_by, :doc_description)}>
            <%= table_sort_icon(@sort_by, :doc_description) %>
          </a>
        </div>
      </th>
      <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">
        <div class="flex items-center">
          File Type
          <a href="#" phx-click="table_sort" phx-value-sort_by="doc_name" phx-value-sort_dir={reverse_sort_order(@sort_by, :doc_name)}>
            <%= table_sort_icon(@sort_by, :doc_name) %>
          </a>
        </div>
      </th>
      <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">
        <div class="flex items-center">
          Status
          <a href="#" phx-click="table_sort" phx-value-sort_by="status" phx-value-sort_dir={reverse_sort_order(@sort_by, :status)}>
            <%= table_sort_icon(@sort_by, :status) %>
          </a>
        </div>
      </th>
      <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Uploaded By</th>
      <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">
        <div class="flex items-center">
          Date Uploaded
          <a href="#" phx-click="table_sort" phx-value-sort_by="inserted_at" phx-value-sort_dir={reverse_sort_order(@sort_by, :inserted_at)}>
            <%= table_sort_icon(@sort_by, :inserted_at) %>
          </a>
        </div>
      </th>
      <th scope="col" class="relative whitespace-nowrap py-3.5 pl-3 pr-4 sm:pr-0">
        <span class="sr-only">Actions</span>
      </th>
    </tr>
  </:thead>
  <:tbody>
    <tr :if={@files == []} class="border-t border-gray-200">
      <th colspan="7" scope="colgroup" class="bg-gray-50 py-2 pl-4 pr-3 text-center text-sm font-semibold text-gray-900 sm:pl-3">No data available</th>
    </tr>
    <tr :for={file <- @files} class="even:bg-gray-50">
      <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500">
        <%= String.replace(file.doc_description, "#{file.doc_name}_", "") %>
      </td>
      <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500">
        <%= if file.doc_name =="ALL_DEAL" do %> All deal <% end %>
        <%= if file.doc_name =="CCR" do %> Customer Contribution <% end %>
        <%= if file.doc_name =="CUST_SEG" do %> GBM File <% end %>
        <%= if file.doc_name =="LOAN_AD" do %> Loans and Advances <% end %>
        <%= if file.doc_name =="FX_CASH" do %> Fx Cash Flow <% end %>
        <%= if file.doc_name =="RELIEF_LIST" do %> Relief List <% end %>
        <%= if file.doc_name =="GOVERNMENT_EXPOSURE_LIST" do %> Government Exposure List <% end %>
        <%= if file.doc_name =="CREDIT_LINE" do %> Credit lines available to the bank <% end %>
        <%= if file.doc_name =="RISK_FILE" do %> Risk File <% end %>
        <%= if file.doc_name =="DAYS_PAST_DUE" do %> Days Past Due <% end %>
        <%= if file.doc_name =="ACCOUNT_DOMICILE_BRANCH" do %> Account Domicile Branch <% end %>
        <%= if file.doc_name =="GDP_FILE" do %> GDP File <% end %>
        <%= if file.doc_name =="RISK_BSA" do %> Weekly Forex risk Exposure(BSA Submission) <% end %>
        <%= if file.doc_name =="CMMP" do %> CMMP <% end %>
        <%= if file.doc_name =="SECTORS" do %> Sectors <% end %>
        <%= if file.doc_name =="LOAN_SCHEME_CODES" do %> Scheme Codes <% end %>
        <%= if file.doc_name =="OBDDR" do %> OBDDR <% end %>
        <%= if file.doc_name =="LARGE_LOANS" do %> Large Loans <% end %>
        <%= if file.doc_name =="INSIDER_LENDING" do %> Insider Lending <% end %>
        <%= if file.doc_name =="CREDIT_CARDS" do %>Credit Cards <% end %>
        <%= if file.doc_name =="CUST_CODE" do %>Customer account details<% end %>
      </td>
      <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500"><%= file.status %></td>
      <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500"><%= "#{file.maker && file.maker.first_name} #{file.maker && file.maker.last_name}" %></td>
      <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500"><%= Timex.format!(file.inserted_at, "%d/%m/%Y %H:%M:%S", :strftime) %></td>
      <td class="relative whitespace-nowrap py-2 pl-3 pr-4 text-right text-sm sm:pr-0">
        <div class="relative px-5 pt-2">
          <button class="focus:ring-2 rounded-md focus:outline-none font-medium" phx-click={table_dropdown(file.id)} phx-click-away={table_dropdown()} role="button" aria-label="option">
            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
            </svg>
          </button>
          <div
            id={"dropdown-#{file.id}"}
            class="tbl-dropdown absolute text-left right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="menu-button"
            tabindex="-1"
          >
            <div class="py-1" role="none">
              <.link
                navigate={}
                class="text-gray-700 flex items-center px-4 py-2 text-sm"
                role="menuitem"
                tabindex="-1"
                id={"view-file-#{file.id}"}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 00-1.883 2.542l.857 6a2.25 2.25 0 002.227 1.932H19.05a2.25 2.25 0 002.227-1.932l.857-6a2.25 2.25 0 00-1.883-2.542m-16.5 0V6A2.25 2.25 0 016 3.75h3.879a1.5 1.5 0 011.06.44l2.122 2.12a1.5 1.5 0 001.06.44H18A2.25 2.25 0 0120.25 9v.776"
                  />
                </svg>
                View Details
              </.link>
            </div>
          </div>
        </div>
      </td>
    </tr>
  </:tbody>
</.table>
