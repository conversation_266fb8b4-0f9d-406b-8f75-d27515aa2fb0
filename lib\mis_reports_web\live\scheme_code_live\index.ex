
defmodule MisReportsWeb.LoanSchemeCodeLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  import Ecto.Query, warn: false
  alias MisReports.Repo
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.Prudentials.LoanSchemeCodes
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Prudentials
  alias MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.UserController

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000]
     )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]
    if UserLiveAuth.authorize?(socket, opts) do

      socket =
        socket
        |> assign(:process_id, params["process_id"])
        |> assign(:reference, params["reference"])
        |> assign(:step_id, params["step_id"])

      {:noreply,
       socket
       |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    loan_scheme_code = Prudentials.get_loan_scheme_code!(id)
    socket
    |> assign(:loan_scheme_code, loan_scheme_code)
  end

  defp apply_action(socket, :new, _params) do
    assign(socket, :loan_scheme_code, %LoanSchemeCodes{})
  end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        # |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        loan_scheme_code = Prudentials.get_loan_scheme_code_by_reference!(reference)

        socket
        |> assign(:loan_scheme_code, loan_scheme_code)
        |> assign(:changeset, Prudentials.change_loan_scheme_code(loan_scheme_code))
        # Explicitly reassign reference
        |> assign(:reference, reference)
    end
  end

  defp apply_action(socket, :index, _params), do: list_loan_scheme_codes(socket)

  @impl true
  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :delete]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_loan_scheme_codes()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_loan_scheme_codes()}
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_loan_scheme_codes()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_by, "sort_dir" => sort_dir}, socket) do
    sort_by_atom = String.to_atom(sort_by)
    sort_dir_atom = String.to_atom(sort_dir)
    updated_socket = assign(socket, sort_by: {sort_dir_atom, sort_by_atom})
    {:noreply, list_loan_scheme_codes(updated_socket)}
  end

  defp handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    loan_scheme_code = Prudentials.get_loan_scheme_code!(id)
    audit_msg = "changed status for template: \"#{loan_scheme_code.scheme_code_description}\", to: #{status}"
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      LoanSchemeCodes.changeset(loan_scheme_code, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _loan_scheme_code, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Operation Successful!")
         |> push_redirect(to: Routes.loan_scheme_code_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp handle_delete(params, socket) do
    id = params["id"]
    loan_scheme_code = Prudentials.get_loan_scheme_code!(id)
    audit_msg = "Deleted loan scheme code: \"#{loan_scheme_code.scheme_code}\""
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_loan_scheme_code, loan_scheme_code)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_loan_scheme_code: _loan_scheme_code, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Loan Scheme Deleted successfully!")
         |> push_redirect(to: Routes.loan_scheme_code_index_path(socket, :index))}

      {:error, failed_value} ->
        {:error, failed_value}
    end
  end

  defp list_loan_scheme_codes(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Prudentials.list_loan_scheme_codes(params)
        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, loan_scheme_codes: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

  defp put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"scheme_code", "new"}

      act when act in ~w(edit)a ->
        {"scheme_code", "edit"}

      act when act in ~w(update_status)a ->
        {"scheme_code", "update_status"}

      act when act in ~w(delete)a ->
        {"scheme_code", "delete"}

      act when act in ~w(index)a ->
        {"scheme_code", "index"}

      _ ->
        {"scheme_code", "unknown"}
    end
  end
end
