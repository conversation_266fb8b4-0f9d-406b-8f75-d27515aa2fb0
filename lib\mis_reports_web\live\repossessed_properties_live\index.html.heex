<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
  <div class="mt-5 font-semibold text-xl">Repossessed Properties</div>
  <%= if @live_action == :new do %>
    <div class="text-sm">New repossessed properties</div>
  <% end %>
  <%= if @live_action == :edit do %>
    <div class="text-sm">Edit repossessed properties</div>
  <% end %>
  <%= if @live_action == :index do %>
    <div class="text-sm">Repossessed Properties </div>
  <% end %><br>
  
  <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
  <.error :if={live_flash(@flash, :error)} flash={@flash} />

  <%!-- <.live_component :if={@live_action in [:new, :edit]} module={MisReportsWeb.RepossessedPropertiesLive.RepossessedPropertiesComponent} id="new-repossessed_properties" current_user={@current_user} repossed_prop={@repossed_prop} action={@live_action} /> --%>
 <.live_component 
  :if={@live_action in [:new, :edit, :update_status]} 
  module={MisReportsWeb.RepossessedPropertiesLive.RepossessedPropertiesComponent} 
  id="new-repossessed_properties" 
  current_user={@current_user} 
  repossed_prop={@repossed_prop} 
  process_id={@process_id}
  reference={@reference}
  step_id={@step_id}
  action={@live_action}
/>
  <%= if @live_action == :index do %>
      <%= Phoenix.View.render(MisReportsWeb.RepossessedPropertiesView, "repossessed_properties.html", assigns) %>
  <% end %>
  
</div>
  
<.confirm_modal />

<.info_notification />

<.error_notification />
