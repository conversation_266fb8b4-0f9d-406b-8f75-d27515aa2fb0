defmodule MisReportsWeb.TechnologicalInfrastructureLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.UserLiveAuth
  alias MisReports.{Utilities, Repo}
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Utilities.TechnologicalInfrastructure
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.UserController

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000],
       process_id: nil,
       reference: nil,
       step_id: nil
     )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do
      {:noreply,
       socket
       |> assign(:process_id, params["process_id"])
       |> assign(:reference, params["reference"])
       |> assign(:step_id, params["step_id"])
       |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :new, _params) do
    assign(socket, :technological_infrastructure, %TechnologicalInfrastructure{})
    |> assign(:position, "")
    |> assign(:values, nil)
  end

  defp apply_action(socket, :index, _params), do: list_tech_infrustructures(socket)

  defp apply_action(socket, :edit, %{"id" => id}) do
    technological_infrastructure = Utilities.get_technological_infrastructure!(id)
    values = get_next_nil(technological_infrastructure)

    socket
    |> assign(:technological_infrastructure, technological_infrastructure)
    |> assign(:values, values)
    |> assign(:position, "h")
  end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        tech_infra = Utilities.get_technological_infrastructure_by_reference!(reference)
        values = get_next_nil(tech_infra)

        socket
        |> assign(:technological_infrastructure, tech_infra)
        |> assign(:reference, reference)
        |> assign(:values, values)
        |> assign(:position, "h")
    end
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_tech_infrustructures()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_tech_infrustructures()}
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_tech_infrustructures()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:description, :report_date, :status, :maker_id], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)

    {:noreply,
     socket
     |> assign(sort_by: sort_by)
     |> list_tech_infrustructures()}
  end

  def get_next_nil(technological_infrastructure) do
    Map.from_struct(technological_infrastructure)
    |> Map.take([
      :lusaka,
      :central,
      :copperbelt,
      :luapula,
      :western,
      :southern,
      :eastern,
      :muchinga,
      :north_western,
      :northern
    ])
    |> MisReports.Workers.Utils.to_atomic_map()
  end

  defp list_tech_infrustructures(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Utilities.list_tbl_technological_infrastructure(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  def page_name(:new), do: "New Estimates"
  def page_name(:index), do: "Staff Benefits Estimates List"
  def page_name(:edit), do: "Staff Benefits Estimates Edit"

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  @impl true
  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :delete]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    technological_infrastructure = Utilities.get_technological_infrastructure!(id)
    audit_msg = "changed status for technological infrastructure to: #{status}"
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      TechnologicalInfrastructure.changeset(technological_infrastructure, %{
        status: status,
        checker_id: current_user.id
      })
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _loan_sector, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Operation Succesfull!")
         |> push_redirect(to: Routes.technological_infrastructure_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    technological_infrastructure = Utilities.get_technological_infrastructure!(id)

    audit_msg =
      "Deleted technological infrastructure with report date: \"#{technological_infrastructure.report_date}\""

    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_tech_infrastructure, technological_infrastructure)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_tech_infrastructure: _loan_sector, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Technological infrastructure Deleted successfully!")
         |> push_redirect(to: Routes.technological_infrastructure_index_path(socket, :index))}

      {:error, failed_value} ->
        {:error, failed_value}
    end
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"technological_infrastructure", "new"}

      act when act in ~w(edit)a ->
        {"technological_infrastructure", "edit"}

      act when act in ~w(update_status)a ->
        {"technological_infrastructure", "update_status"}

      act when act in ~w(delete)a ->
        {"technological_infrastructure", "delete"}

      act when act in ~w(index)a ->
        {"technological_infrastructure", "index"}

      _ ->
        {"technological_infrastructure", "unknown"}
    end
  end
end
