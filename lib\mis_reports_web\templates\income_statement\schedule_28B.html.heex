
<style>
   *{
   margin: 0;
   padding: 0;
   box-sizing: border-box;
   }
   div.ex1 {
   height: 740px;
   overflow-y: scroll;
   margin-top:3%;
   }
   .bold{
   font-weight: 900;
   }
   .light{
   font-weight: 100;
   }
   .wrapper{
   background: #fff;
   padding: 20px;
   }
   .invoice_wrapper{
   width: 100%;
   max-width: 100%;
   border: 1px solid none
   }
   .invoice_wrapper .header .logo_invoice_wrap,
   .invoice_wrapper .header .bill_total_wrap{
   display:flex;
   justify-content: space-between;
   padding: 30px;
   }
   .invoice_wrapper .header .logo_sec .title_wrap{
  
   }
   .invoice_wrapper .header .logo_sec .title_wrap .title{
   text-transform: uppercase ;
   font-size: 18px;
   color: #0C40A2;
   }
   .invoice_wrapper .header .logo_sec .title_wrap .sub_title{
   font-size: 12px;
   }
   .invoice_wrapper .header .invoice_sec,
   .invoice_wrapper .header .total_wrap{
   text-align: right;
   }
   .invoice_wrapper .header .invoice_sec .invoice{
   font-size: 28px;
   color:blue;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no,
   .invoice_wrapper .header .invoice_sec .date{
   display: flex;
   width: 100%;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
   .invoice_wrapper .header .invoice_sec .date span:first-child{
   width: 70%;
   text-align: left;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
   .invoice_wrapper .header .invoice_sec .date span:first-child{
   width: calc(100%  -70px);
   }
   .invoice_wrapper .header .bill_total_wrap .name
   .invoice_wrapper .header .bill_total_wrap .price{
   font-size: 20px;
   }
   .invoice_wrapper .body .main_table .table_header{
   border-bottom: 1px solid #000;
   }
   .invoice_wrapper .body .main_table .table_header .row{
   color:#000;
   font-size: 18px;
   border-bottom: 0px;
   }
   .invoice_wrapper .body .main_table  .row{
   display: flex;
   border-bottom: 1px solid #e9e9e9;
   }
   .invoice_wrapper .body .main_table .row .col{
   padding: 9px;
   }
   .invoice_wrapper .body .main_table .row .col.col_no{width: 8%;}
   .invoice_wrapper .body .main_table .row .col.col_des{width: 30%;  text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_price{width: 23%; text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_qty{width: 21%;  text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_total{width: 20%;  text-align: center;}
   .invoice_wrapper .body .paymethod_grantotal_wrap{
   display: flex;
   justify-content: space-between;
   padding: 5px 0 30px;
   align-items: flex-end;
   padding-top: 2rem;
   }
  
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p:last-child span{
   border-bottom: 1px solid #000;
   }
   .invoice_wrapper .footer{
   padding:30px;
   }
   .invoice_wrapper .footer{
   padding:30px;
   }
   .kanja
   {
   font-size:16px;
   }
   .kanja_peter
   {
   font-size:13px;
   margin-top:1rem
   }
   table {
    width: 96%;
    margin-left: 3%;
}
   table th {
   border: solid 1px gray;
   text-align: left;
   }
   .kanja_chileshe
   {
   font-weight:100;
   background-color:#ffff99;
   }
   .man{
   background-color:#ffff99;
   }
   .kanja_p
   {
   font-weight:100;
   background-color:#c0c0c0;
   }
   .peter{
   text-align: right;
   }
   .p_kanja
   {
   font-weight:100;
   background-color:#CCCCFD;
   }
   .man{
   background-color:#ffcc99;
   }
   .mans{
   background-color:#aaf05a;
   }
   table,
   th,
   td {
   border: 0.5px solid gray;
   }
   th, td {
   padding: 2px;
   }
   
   .class-name{
    transition: all 1.5s;
    animation: animation-name 0.5s linear;
  }
  
  @keyframes animation-name{
    from{
      transform: translateX(-100%);
      height: 0px;
    }
   }
</style>
<body>
   <div class="wrapper ex1 class-name">
      <div class="invoice_wrapper"  id="to_print">
         <div class="header">
            <div class="logo_invoice_wrap">
               <div class="logo_sec">
                  <img src="">
                  <div class="title_wrap">
                     <p class="title bold">
                        Schedule 28B
                     </p>
                     <p class="sub_title light">ZM-VZSCH28BVZ001</p>
                  </div>
               </div>
               <div class="invoice_sec" style="display:none">
                  <p class="invoice bold"></p>
                  <p class="invoice_no">
                     <span class="bold">Attestation:</span>
                     <span style="margin-left:0.2rem">Pending</span>
                  </p>
                  <p class="date" style="display:none">
                     <span class="bold">date</span>
                     <span><%= Timex.format!(Timex.local,"%e %b %Y", :strftime) %></span>
                  </p>
               </div>
            </div>

            <div class="bill_total_wrap">
               <table style="width:50%;">
                   <tr>
                       <th style="font-weight:500">Institution Name:</th>
                       <th class="p_kanja"> <%= @settings.company_name %></th>
                   </tr>
                   <tr>
                       <th style="font-weight:500">Institution Code:</th>
                       <th class="p_kanja"> <%= @settings.institution_code %></th>
                   </tr>
                   <tr>
                       <th style="font-weight:500">Financial Year:</th>
                       <th class="p_kanja"> <%= Timex.format!(Date.from_iso8601!(@filter_params["start_date"]),"%Y", :strftime) %></th>
                   </tr>
                   <tr>
                       <th style="font-weight:500">Date:</th>
                        <th class="p_kanja"><%= Timex.format!(Date.from_iso8601!(@filter_params["end_date"]),"%e %B %Y", :strftime) %></th>
                   </tr>
               </table>
           </div>

         </div>
         <div class="body" style="margin-top:2%">
            <div class="main_table">
               <table>
                  <tr class="sample">
                     <th colspan="12" style="text-align:center; font-size:13px">OTHER NON-INTEREST INCOME </th>
                  </tr>
                  <tr>
                     
                     <th style="font-size:13px">Account Name </th>
                     <th style="font-size:13px">Amount</th>
                     <th style="font-size:13px">Details</th>
                  </tr>
                  <tr>
                    
                     <td  class="" style="text-align:left">Total</td>
                     <td class="kanja_p" style="text-align:right">
                        <%= if @data do %> 
                              <%= @data[:total] %>
                        <% else %>
                           0
                        <% end %>
                     </td>
                     <td class=""></td>
                  </tr>
                  <%= if @data && @data[:list] do %>
                     <%= for item <- @data[:list] do %>
                        <tr> 
                           <td  class="kanja_chileshe" style="text-align:left"><%= item[:name] %></td>
                           <td class="kanja_chileshe" style="text-align:right"><%= item[:value] %></td>
                           <td class="kanja_chileshe"></td>
                        </tr>
                     <% end %>
                  <% end %>
               </table>
            </div>
         </div>
      </div>
   </div>
</body>