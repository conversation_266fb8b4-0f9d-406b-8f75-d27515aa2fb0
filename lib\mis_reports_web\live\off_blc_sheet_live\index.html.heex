<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
    <div class="mt-5 font-semibold text-xl">Off Balance Sheet</div>
    <%= if @live_action == :new do %>
      <div class="text-sm">New Off Balance Sheet</div>
    <% end %>
    <%= if @live_action == :edit do %>
      <div class="text-sm">Edit Off Balance Sheet</div>
    <% end %>
    <%= if @live_action == :index do %>
      <div class="text-sm">Off Balance Sheet </div>
    <% end %><br>
    
    <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
    <.error :if={live_flash(@flash, :error)} flash={@flash} />

    <%= if @live_action == :update_status do %>  
      <.live_component 
      module={MisReportsWeb.OffBlcSheetLive.OffBlcSheetComponent} 
      id="new-off_blc_sheet" 
      current_user={@current_user} 
      off_blc_sheet={@off_blc_sheet} 
      changeset={@changeset}
      process_id={@process_id}
      reference={@reference}
      step_id={@step_id}
      action={@live_action} />

    <% end %>

    <.live_component 
    :if={@live_action in [:new, :edit]} 
    module={MisReportsWeb.OffBlcSheetLive.OffBlcSheetComponent} 
    id="new-off_blc_sheet" 
    current_user={@current_user} 
    off_blc_sheet={@off_blc_sheet} 
    process_id={@process_id}
    reference={@reference}
    step_id={@step_id}
    action={@live_action} />
   
    <%= if @live_action == :index do %>
        <%= Phoenix.View.render(MisReportsWeb.OffBlcSheetView, "off_blc_sheets.html", assigns) %>
    <% end %>
    
</div>
    
<.confirm_modal />

<.info_notification />

<.error_notification />

