defmodule MisReportsWeb.OffBlcSheetLive.OffBlcSheetComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Utilities, Repo}
  alias MisReports.Utilities.OffBlcSheet
  alias MisReportsWeb.OffBlcSheetController
  alias MisReportsWeb.UserController

  import MisReportsWeb.UserLive.Index, only: [put_conn_user: 1]

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.OffBlcSheetView, "off_blc_sheet.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.OffBlcSheetView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{off_blc_sheet: off_blc_sheet} = assigns, socket) do
    changeset = Utilities.change_off_blc_sheet(off_blc_sheet)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"off_blc_sheet" => params}, socket) do
    changeset =
      socket.assigns.off_blc_sheet
      |> OffBlcSheet.changeset(params)
      |> Map.put(:action, :validate)
    {:noreply, assign(socket, :changeset, changeset)}
  end

  @impl true
  def handle_event("save", %{"off_blc_sheet" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_save(socket, :reject, params)
      "97" -> handle_save(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp handle_save(socket, :new, params) do
    audit_msg = "Created new off_blc_sheet with report date \"#{params["report_dt"]}\" "
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:off_blc_sheet, OffBlcSheet.changeset(%OffBlcSheet{maker_id: user.id}, params))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{off_blc_sheet: off_blc_sheet}} ->
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Off Balance Sheet Creation"
             ) do
          {:ok, reference_number} ->
            case Utilities.update_off_blc_sheet(off_blc_sheet, %{reference: reference_number}) do
              {:ok, updated_off_blc_sheet} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "Off Balance Sheet created successfully. Reference: #{reference_number}")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update off balance sheet reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Off Balance Sheet created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  defp handle_save(socket, :edit, params) do
    off_blc_sheet = socket.assigns.off_blc_sheet

    socket
    |> handle_update(params, off_blc_sheet)
    |> case do
      {:ok, off_blc_sheet} ->
        {:noreply,
         socket
         |> put_flash(:info, "Off Balance Sheet updated successfully")
         |> push_redirect(to: Routes.off_blc_sheet_index_path(socket, :index))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  defp handle_save(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Off Balance Sheet Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Off Balance Sheet rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}
      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject off balance sheet: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp handle_save(socket, :approve, params) do
    off_blc_sheet = socket.assigns.off_blc_sheet
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Off Balance Sheet Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      OffBlcSheet.changeset(off_blc_sheet, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_off_blc_sheet}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Off Balance Sheet approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}
          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Off Balance Sheet approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end
      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve off balance sheet")
         |> assign(:changeset, %{off_blc_sheet.changeset | errors: failed_value.errors})}
    end
  end

  def handle_update(socket, params, off_blc_sheet) do
    audit_msg = "Updated off balance sheet with report date \"#{params["report_dt"]}\" "

    updated_changeset =
      off_blc_sheet
      |> OffBlcSheet.changeset(params)
      |> Ecto.Changeset.put_change(:status, "D")
      |> Ecto.Changeset.put_change(:checker_id, nil)
      |> Ecto.Changeset.put_change(:maker_id, socket.assigns.current_user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:off_blc_sheet, updated_changeset)
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{off_blc_sheet: off_blc_sheet, audit_log: _user_log}} ->
        {:ok, off_blc_sheet}
      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end
end
