defmodule TechnologicalInfrastructureLive.TechnologicalInfrastructureComponent do
  use MisReportsWeb, :live_component
  use PipeTo.Override
  alias MisReports.{Repo, Utilities}
  alias MisReports.Utilities.TechnologicalInfrastructure
  alias MisReportsWeb.UserController

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.TechnologicalInfrastructureView, "new.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.TechnologicalInfrastructureView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{technological_infrastructure: technological_infrastructure} = assigns, socket) do
    changeset = Utilities.change_technological_infrastructure(technological_infrastructure)

    # Add workflow params
    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" ->
        save_technological_infrastructure(socket, :reject, params)

      "97" ->
        save_technological_infrastructure(socket, :approve, params)

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_event("save", %{"technological_infrastructure" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  defp handle_save(socket, :new, params) do
    audit_msg = "Created new Technological Infrastructure for date: #{params["report_date"]}"
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :technological_infrastructure,
      TechnologicalInfrastructure.changeset(
        %TechnologicalInfrastructure{maker_id: user.id},
        params
      )
    )
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{technological_infrastructure: tech_infra}} ->
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Technological Infrastructure Creation"
             ) do
          {:ok, reference_number} ->
            case Utilities.update_technological_infrastructure(tech_infra, %{
                   reference: reference_number
                 }) do
              {:ok, updated_tech_infra} ->
                {:noreply,
                 socket
                 |> put_flash(
                   :info,
                   "Technological Infrastructure created successfully. Reference: #{reference_number}"
                 )
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  defp save_technological_infrastructure(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Technological Infrastructure Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp save_technological_infrastructure(socket, :approve, params) do
    tech_infra = socket.assigns.technological_infrastructure
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Technological Infrastructure Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      TechnologicalInfrastructure.changeset(tech_infra, %{
        status: "A",
        checker_id: current_user.id
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_tech}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve")
         |> assign(:changeset, failed_value)}
    end
  end

  defp handle_save(socket, :edit, params) do
    technological_infrastructure = socket.assigns.technological_infrastructure

    socket
    |> handle_update(params, technological_infrastructure)
    |> case do
      {:ok, technological_infrastructure} ->
        {:noreply,
         socket
         |> put_flash(:info, "Technological infrastructure updated successfully")
         |> push_redirect(
           to:
             Routes.technological_infrastructure_index_path(
               socket,
               :edit,
               technological_infrastructure
             )
         )}

      {:error, %Ecto.Changeset{} = _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update")
         |> push_redirect(
           to:
             Routes.technological_infrastructure_index_path(
               socket,
               :edit,
               technological_infrastructure
             )
         )}
    end
  end

  defp handle_update(socket, params, technological_infrastructure) do
    audit_msg =
      "Initiated technological infrastructure update for report date \"#{technological_infrastructure.report_date}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :technological_infrastructure,
      TechnologicalInfrastructure.changeset(
        technological_infrastructure,
        Map.merge(params, %{"status" => "D", "checker_id" => nil})
      )
    )
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{technological_infrastructure: technological_infrastructure, audit_log: _user_log}} ->
        {:ok, technological_infrastructure}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end
end
