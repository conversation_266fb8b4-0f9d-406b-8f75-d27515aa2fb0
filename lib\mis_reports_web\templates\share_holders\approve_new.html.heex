
<style>
   *{
   margin: 0;
   padding: 0;
   box-sizing: border-box;
   }
   div.ex1 {
   height: 720px;
   overflow-y: scroll;
   margin-top:3%;
   }
   .bold{
   font-weight: 900;
   }
   .light{
   font-weight: 100;
   }
   .wrapper{
   background: #fff;
   padding: 30px;
   }
   .invoice_wrapper{
   width: 150%;
   max-width: 150%;
   border: 1px solid none
   }
   .invoice_wrapper .header .logo_invoice_wrap,
   .invoice_wrapper .header .bill_total_wrap{
   display:flex;
   justify-content: space-between;
   padding: 30px;
   }
   .invoice_wrapper .header .logo_sec .title_wrap{
   margin-left: 5px;
   }
   .invoice_wrapper .header .logo_sec .title_wrap .title{
   text-transform: uppercase ;
   font-size: 18px;
   color: #0C40A2;
   }
   .invoice_wrapper .header .logo_sec .title_wrap .sub_title{
   font-size: 12px;
   }
   .invoice_wrapper .header .invoice_sec,
   .invoice_wrapper .header .total_wrap{
   text-align: right;
   }
   .invoice_wrapper .header .invoice_sec .invoice{
   font-size: 28px;
   color:blue;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no,
   .invoice_wrapper .header .invoice_sec .date{
   display: flex;
   width: 100%;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
   .invoice_wrapper .header .invoice_sec .date span:first-child{
   width: 70%;
   text-align: left;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
   .invoice_wrapper .header .invoice_sec .date span:first-child{
   width: calc(100%  -70px);
   }
   .invoice_wrapper .header .bill_total_wrap .name
   .invoice_wrapper .header .bill_total_wrap .price{
   font-size: 20px;
   }
   .invoice_wrapper .body .main_table .table_header{
   border-bottom: 1px solid #000;
   }
   .invoice_wrapper .body .main_table .table_header .row{
   color:#000;
   font-size: 18px;
   border-bottom: 0px;
   }
   .invoice_wrapper .body .main_table  .row{
   display: flex;
   border-bottom: 1px solid #e9e9e9;
   }
   .invoice_wrapper .body .main_table .row .col{
   padding: 9px;
   }
   .invoice_wrapper .body .main_table .row .col.col_no{width: 8%;}
   .invoice_wrapper .body .main_table .row .col.col_des{width: 30%;  text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_price{width: 23%; text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_qty{width: 21%;  text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_total{width: 20%;  text-align: center;}
   .invoice_wrapper .body .paymethod_grantotal_wrap{
   display: flex;
   justify-content: space-between;
   padding: 5px 0 30px;
   align-items: flex-end;
   padding-top: 2rem;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .paymethod_sec{
   padding-left: 30px;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec{
   width: 20%;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p{
   display: flex;
   width: 100%;
   padding-bottom: 5px;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span{
   padding: 0 10px;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:first-child{
   width: 60%;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:last-child span{
   width: 40%;
   text-align: right;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p:last-child span{
   border-bottom: 1px solid #000;
   }
   .invoice_wrapper .footer{
   padding:30px;
   }
   .invoice_wrapper .footer{
   padding:30px;
   }
   .kanja
   {
   font-size:16px;
   }
   .kanja_peter
   {
   font-size:13px;
   margin-top:1rem
   }
   .peter{
   text-align: right;
   }
   table {
   width: 87%;
   }
   table th {
   border: solid 1px gray;
   text-align: left;
   }
   .kanja_chileshe
   {
   font-weight:100;
   background-color:#ffff99;
   }
   .kanja_p
   {
   font-weight:100;
   background-color:#c0c0c0;
   }
   .p_kanja
   {
   font-weight:100;
   background-color:#ccccff;
   }
   .man{
   font-weght:400;
   }
   .mrt{
   margin-top:5%;
   font-weight:800
   }
   .mans{
   background-color:#aaf05a;
   }
   .yellow{
   background-color:#ffcc99;
   }
   .yellows{
   background-color:#ffff99;
   }
   th, td {
   padding: 2px;
   }

.ex1{
   transition: all 1.5s;
   animation: animation-name 0.5s linear;
}

@keyframes animation-name{
   from{
   transform: translateX(-100%);
   height: 0px;
   }
}
</style>
<body>
   <.form :let={f} for={@changeset} as={:share_holders} id="share_holders" phx-submit="save" phx-target={@myself} >
   <div class="wrapper ex1" style="width: 110%;">
      <div class="invoice_wrapper"  id="to_print">
         <div class="header">
            <div class="logo_invoice_wrap">
               <div class="logo_sec">
                  <img src="">
               </div>
            </div>
            <div class="bill_total_wrap">
               <table style="width:20%; margin-left:-1%">
                  <tr>
                     <th style="font-weight:500">Report date:</th>
                     <th class="p_kanja"> 
                        <%= date_input(
                           f,
                           :report_date,
                           class: "h-6  w-full  p_kanja  text-gray-900 ",
                           required: "true" ,
                           disabled: "true"   
                           ) %> <%= error_tag(f, :report_date) %>
                     </th>
                  </tr>
               </table>
            </div>
         </div>
         <p class="mrt flex justify-center">CHANGES IN SHAREHOLDERS' EQUITY (CONT'D)</p>
         <p class="mrt flex ">RETAINED EARNINGS / RESERVES</p>
         <div class="body">
            <div class="main_table" >
               <table>
                  <thead>
                     <tr class="sample">
                        <th  style="font-size:13px; font-weight:800" ></th>
                        <th  style="font-size:13px; font-weight:800" ></th>
                        <th  style="font-size:13px; font-weight:800" >Retained Earnings</th>
                        <th  style="font-size:13px; font-weight:800" >Fair Value Reserve</th>
                        <th  style="font-size:13px; font-weight:800" >Revaluation Reserves</th>
                        <th  style="font-size:13px; font-weight:800" >Statutory Reserves</th>
                        <th  style="font-size:13px; font-weight:800" >Other Reserves</th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100">1</th>
                        <th style="font-weight:200">Balance at beginning of Month</th>
                        <th class="">
                           <%= number_input(
                              f,
                              :c22,
                              required: "true",
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :C22) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :d22,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 ",  
                              disabled: "true"  
                              ) %> <%= error_tag(f, :D22) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :e22,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 "  ,
                              disabled: "true" 
                              ) %> <%= error_tag(f, :E22) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :f22,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " , 
                              disabled: "true" 
                              ) %> <%= error_tag(f, :F22) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :g22,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 ",
                              disabled: "true"   
                              ) %> <%= error_tag(f, :G22) %>
                        </th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100">2</th>
                        <th style="font-weight:200">Net income (loss) for the Month (after taxes) (item 13 of statement of income)</th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :c24,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :c24) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :d24,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"  
                              ) %> <%= error_tag(f, :d24) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :e24,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"  
                              ) %> <%= error_tag(f, :E24) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :f24,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :f24) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :g24,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :g24) %>
                        </th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100">3</th>
                        <th style="font-weight:200">Prior period adjustments (net of income taxes)</th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :c26,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 ",
                              disabled: "true"    
                              ) %> <%= error_tag(f, :c26) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :e26,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 "  ,
                              disabled: "true"  
                              ) %> <%= error_tag(f, :e26) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :d26,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :d26) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :f26,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"  
                              ) %> <%= error_tag(f, :f26) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :g26,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true" 
                              ) %> <%= error_tag(f, :g26) %>
                        </th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100">4</th>
                        <th style="font-weight:200">Fair value gains and (losses), net of tax:Other Comprehensive Income</th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :c28,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true" 
                              ) %> <%= error_tag(f, :c28) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :d28,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 ",
                              disabled: "true"    
                              ) %> <%= error_tag(f, :d28) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :e28,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true" 
                              ) %> <%= error_tag(f, :e28) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :f28,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true" 
                              ) %> <%= error_tag(f, :f28) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :g28,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 ",
                              disabled: "true"    
                              ) %> <%= error_tag(f, :g28) %>
                        </th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100">5</th>
                        <th style="font-weight:200">Cash flow hedges, net of tax</th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :c31,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"    
                              ) %> <%= error_tag(f, :c31) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :d31,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true" 
                              ) %> <%= error_tag(f, :d31) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :e31,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900",
                              disabled: "true"     
                              ) %> <%= error_tag(f, :e31) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :f31,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900" ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :f31) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :g31,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900" ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :g31) %>
                        </th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100">6</th>
                        <th style="font-weight:200">Transfers to/(from) Retained Earnings</th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :c33,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 ",
                              disabled: "true"    
                              ) %> <%= error_tag(f, :c33) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :d33,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"    
                              ) %> <%= error_tag(f, :d33) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :e33,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"    
                              ) %> <%= error_tag(f, :e33) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :f33,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :f33) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :g33,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :g33) %>
                        </th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100">7</th>
                        <th style="font-weight:200">Dividends</th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :c35,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 ",
                              disabled: "true"   
                              ) %> <%= error_tag(f, :c35) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :d35,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :d35) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :e35,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"  
                              ) %> <%= error_tag(f, :e35) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :f35,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :f35) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :g35,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true" 
                              ) %> <%= error_tag(f, :g35) %>
                        </th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100">8</th>
                        <th style="font-weight:200">Other increases/(decreases) during the Month</th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :c37,
                              class: "w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"    
                              ) %> <%= error_tag(f, :c37) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :d37,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"  
                              ) %> <%= error_tag(f, :d37) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :e37,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"  
                              ) %> <%= error_tag(f, :e37) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :f37,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"  
                              ) %> <%= error_tag(f, :f37) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :g37,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"   
                              ) %> <%= error_tag(f, :g37) %>
                        </th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                     </tr>
                     <tr>

                        <th style="font-size:13px; font-weight:100">9</th>
                        <th style="font-weight:200">Balance at end of Month</th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :c39,
                              class: "w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"    
                              ) %> <%= error_tag(f, :c39) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :d39,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"
                              ) %> <%= error_tag(f, :d39) %>
                        </th>
                        <th class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :e39,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"
                              ) %> <%= error_tag(f, :e39) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :f39,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"
                              ) %> <%= error_tag(f, :f39) %>
                        </th>
                        <th class="kanja_chileshe">
                           <%= number_input(
                              f,
                              :g39,
                              class: " w-full h-6   kanja_chileshe peter  text-gray-900 " ,
                              disabled: "true"
                              ) %> <%= error_tag(f, :g39) %>
                        </th>
                     </tr>
                     <tr>
                        <th style="font-size:13px; font-weight:100"></th>
                        <th style="font-weight:200; color:white">-</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                       
                     </tr>
                  </thead>
               </table>
            </div>
            <div class="mt-6 flex items-center justify-end gap-x-6">
            <button 
              type="submit" 
              phx-click="save"
              phx-target={@myself}
              phx-value-action="97"
              class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600">
              Approve
            </button>
    
            <button 
              type="submit"
              phx-click="save"
              phx-target={@myself}
              phx-value-action="96"
              class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
              Reject
            </button>
          </div>
         </div>
      </div>
   </div>
   </.form>
</body>