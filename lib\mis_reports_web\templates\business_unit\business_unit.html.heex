<.form :let={f} for={@changeset} as={:business_unit} id="business-unit-form" phx-submit="save" phx-change="validate" phx-target={@myself}>
    <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
      <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
        <div class="-ml-4 -mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
          <div class="ml-4 mt-4">
            <div class="flex items-center">
              <span class="inline-block h-10 w-10 overflow-hidden rounded-full bg-gray-100">
                <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </span>
  
              <div class="ml-4">
                <h3  class="text-base font-semibold leading-6 text-gray-900">Create Business Unit</h3>
                <p class="text-sm text-gray-500">
                  <a href="#">Bussiness Unit Details</a>
                </p>
              </div>
            </div>
          </div>
          <div class="ml-4 mt-4 flex flex-shrink-0">

            <.link navigate={Routes.business_unit_index_path(@socket,:index)} class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100 " role="menuitem" tabindex="-1" id="menu-item-0">
            <button type="button" class="relative inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="-ml-0.5 mr-1.5 h-5 w-5 text-gray-400">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"
                />
              </svg>
              <span>View Business Units</span>
            </button>
            </.link>
          </div>
        </div>
      </div>
  
      <div class="px-4 py-5 sm:p-6">
        <div class="space-y-12">
          <div class="border-b border-gray-900/10 pb-12">
            <div class="mt-8 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
  
            <div class="sm:col-span-3">
                <label for="last-facility_category" class="block text-sm font-medium leading-6 text-gray-900">CCR Business Unit</label>
                <div class="mt-2">
                  <%= text_input(
                    f,
                    :ccr_business_unit,
                    autocomplete: "CCR Business Unit",
                    placeholder: "CCR Business Unit",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true"
                  ) %> <%= error_tag(f, :ccr_business_unit) %>
                </div>
              </div>
  
              <div class="sm:col-span-3">
                <label for="business_unit" class="block text-sm font-medium leading-6 text-gray-900">Business unit</label>
                <div class="mt-2">
                  <%= text_input(
                    f,
                    :business_unit,
                    autocomplete: "Business Unit",
                    placeholder: "Business_unit",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true"
                  ) %> <%= error_tag(f, :business_unit) %>
                </div>
              </div>
  
              <div class="sm:col-span-3">
                <label for="facility_category" class="block text-sm font-medium leading-6 text-gray-900">Facility category</label>
                <div class="mt-2">
                  <%= text_input(
                    f,
                    :facility_category,
                    autocomplete: "Facility Category",
                    placeholder: "Facility Category",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true"
                  ) %> <%= error_tag(f, :facility_category) %>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-6 flex items-center justify-end gap-x-6">
          <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
        </div>
      </div>
    </div>
</.form>
  