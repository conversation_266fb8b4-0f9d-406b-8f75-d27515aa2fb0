<div class="space-y-10 divide-y divide-gray-900/10">
   <div class="grid grid-cols-1 gap-x-8 gap-y-8 pt-10 md:grid-cols-3">
      <div class="px-4 sm:px-0">
         <h2 class="text-base font-semibold leading-7 text-gray-900">Add new specification</h2>
         <p class="mt-1 text-sm leading-6 text-gray-600">File specification includes the template name, read line and columns to be mapped.</p>
      </div>
      <.form :let={f} for={@changeset} class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2" as={:file_spec} id="user-form" phx-submit="add-column" phx-change="validate" phx-target={@myself}>
        <div class="px-4 py-6 sm:p-8">
          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
              <div class="sm:col-span-3">
                <label for="template-name"  class="block text-sm font-medium leading-6 text-gray-900">Template name</label>
                <div class="mt-2">
                    <%= select(
                      f,
                      :temp_name,
                      [
                        {"All Deal", "all_deal"},
                        {"Customer Contribution", "cust_contribution"},
                        {"GBM File", "cust_segmt"},
                        {"Loan and advances", "loan_ad"},
                        {"Fx Cash Flow", "fx_cash"},
                        {"Relief List", "relief_list"},
                        {"Government Exposure List", "government_exposure_list"},
                        {"Credit lines available to the Bank", "credit_line_avbl"},
                        {"Sectors", "loan_sectors"},
                        {"Loan Scheme Codes", "loan_scheme_codes"},
                        {"Loan Products", "loan_products"},
                        {"OBDDR", "obddr"},
                        {"Large Loans", "large_loans"},
                        {"Insider Lending", "insider_lending"},
                        {"Credit Cards", "credit_cards"},
                        {"Days Past Due", "days_past_due"},
                        {"Account Domicile Branch", "account_domicile_branch"},
                        {"GDP File", "gdp_file"},
                        {"NOSTRO", "nostro"},
                        {"Cmmp Products", "cproducts"},
                        {"Cmmp Branches", "cbranches"}
                      ],
                      prompt: [key: "Select template", disabled: true],
                      selected: input_value(f, :temp_name),
                      phx_click: "get_file_sec",
                      autocomplete: "Template-name",
                      class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                      required: "true"
                      ) %> <%= error_tag(f, :temp_name) %>
                </div>
              </div>
              <div class="sm:col-span-3">
                <label for="Read-line" class="block text-sm font-medium leading-6 text-gray-900">Read line</label>
                <div class="mt-2">
                    <%= text_input(
                      f,
                      :read_line,
                      autocomplete: "Read Line",
                      placeholder: "Read-line",
                      class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                      required: "true"
                      ) %> <%= error_tag(f, :read_line) %>
                </div>
              </div>
              <div class="sm:col-span-3">
                <label for="column-name" class="block text-sm font-medium leading-6 text-gray-900">Column name</label>
                <div class="mt-2">
                    <%= select(
                      f,
                      :col_name,
                      Enum.map(@file_spec_cols, &{elem(&1.col_name, 1), elem(&1.col_name, 0)}),
                      prompt: [key: "Select column", disabled: true],
                      selected: input_value(f, :col_name),
                      autocomplete: "column-name",
                      class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                      required: "true"
                      ) %> <%= error_tag(f, :col_name) %>
                </div>
              </div>
              <div class="sm:col-span-3">
                <label for="column-name"  class="block text-sm font-medium leading-6 text-gray-900">Column index</label>
                <div class="mt-2">
                    <%= text_input(
                      f,
                      :col_index,
                      autocomplete: "column-name",
                      placeholder: "column-name",
                      class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                      required: "true"
                      ) %> <%= error_tag(f, :col_index) %>
                </div>
              </div>
              <div class="col-span-full" >
                <div class="flex items-center justify-end gap-x-6">
                  <button type="reset" name="reset" class="bg-white hover:bg-indigo-600 font-semibold  text-xs border border-gray-300 text-gray-800 py-2 px-4 rounded inline-flex items-center">
                    <svg class="fill-current w-4 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M4.755 10.059a7.5 7.5 0 0112.548-3.364l1.903 1.903h-3.183a.75.75 0 100 1.5h4.992a.75.75 0 00.75-.75V4.356a.75.75 0 00-1.5 0v3.18l-1.9-1.9A9 9 0 003.306 9.67a.75.75 0 101.45.388zm15.408 3.352a.75.75 0 00-.919.53 7.5 7.5 0 01-12.548 3.364l-1.902-1.903h3.183a.75.75 0 000-1.5H2.984a.75.75 0 00-.75.75v4.992a.75.75 0 001.5 0v-3.18l1.9 1.9a9 9 0 0015.059-4.035.75.75 0 00-.53-.918z"/>
                    </svg>
                    <span>Reset</span>
                  </button>
                  <button type="submit" class="bg-white hover:bg-indigo-600 font-semibold  text-xs border border-gray-300 text-gray-800 py-2 px-6 rounded inline-flex items-center">
                    <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M12 5.25a.75.75 0 01.75.75v5.25H18a.75.75 0 010 1.5h-5.25V18a.75.75 0 01-1.5 0v-5.25H6a.75.75 0 010-1.5h5.25V6a.75.75 0 01.75-.75z"/>
                    </svg>
                    <span>Add column </span>
                  </button>
                </div>
              </div>
              <div class="col-span-full">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                      <h1 class="text-sm font-semibold leading-6 text-gray-900">Columns </h1>
                    </div>
                </div>
                <div class="-mx-4 mt-5 ring-1 ring-gray-300 sm:mx-0 sm:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300 mb-5">
                      <thead>
                          <tr>
                            <th scope="col" class="py-2 pl-10 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-6">S/N</th>
                            <th scope="col" class="hidden px-3 py-2 text-left text-xs font-semibold text-gray-900 lg:table-cell">Column Name</th>
                            <th scope="col" class="text-xs font-semibold  text-left text-gray-900">Column Index</th>
                          </tr>
                      </thead>
                      <tbody>
                          <tr :if={@columns == []}>
                            <th colspan="7" scope="colgroup" class="py-2 pl-4 pr-3 text-center rounded-lg bg-gray-50 text-sm font-semibold text-gray-900 sm:pl-3">No data available</th>
                          </tr>
                          <tr class="border border-gray-300 rounded-sm" :for={{column, index} <- Enum.with_index(@columns, 1)}>
                            <td class="relative py-2 pl-4 pr-3 text-xs sm:pl-6">
                              <%= index %>
                            </td>
                            <td class="px-3 py-2 text-xs">
                                <div class="border-gray-300 hidden sm:block">
                                  <%= case Enum.find(@file_spec_cols, fn col -> elem(col.col_name, 0) == String.to_existing_atom(column.col_name) end) do %>
                                     <% nil -> %> <%= nil %>

                                     <% pattern -> %><%= elem(pattern.col_name, 1) %>

                                    <% end %>
                                </div>
                            </td>
                            <td class="relative py-2 pl-3 pr-4 text-xs sm:pr-6 pl-14 items-right justify-end">
                                <div class="bg-white px-3 py-1.5 text-xs text-gray-900">
                                  <%= column.col_index %>
                                </div>
                            </td>
                          </tr>
                          <!-- More rows... -->
                      </tbody>
                    </table>
                </div>
              </div>
          </div>
        </div>
        <div class="flex items-center justify-end gap-x-6 border-t border-gray-900/10 px-4 py-4 sm:px-8">
         <button type="button" phx-click="save" phx-target={@myself} class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
        </div>
      </.form>
   </div>
</div>
