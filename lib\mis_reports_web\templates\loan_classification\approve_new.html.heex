<style>

  .ex1{
     transition: all 1.5s;
     animation: animation-name 0.5s linear;
  }
  
  @keyframes animation-name{
     from{
     transform: translateX(-100%);
     height: 0px;
     }
  }
  </style>





<.form :let={f} for={@changeset} as={:loan_classification} id="business-unit-form" phx-submit="save" phx-change="validate" phx-target={@myself}>
  <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
    <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
      <div class="ml-4 mt-4">
        <h3 class="text-base font-semibold leading-6 text-gray-900">Loan Classifications for Approval</h3>
        <p class="text-sm text-gray-500">Review loan classifications</p>
      </div>
    </div>

    <div class="px-4 py-5 sm:p-6">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-300">
          <thead>
            <tr>
              <th class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">Classification</th>
              <th class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Days Past Due</th>
              <th class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Past Due NPL Classification</th>
              <th class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">Date Created</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <%= for loan <- @loan_classifications do %>
              <tr class="even:bg-gray-50">
                <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0"><%= loan.loan_classification %></td>
                <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500"><%= loan.days_past_due %></td>
                <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500"><%= loan.past_due_npl_classification %></td>
                <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500"><%= Timex.format!(loan.inserted_at, "%d %b, %Y %H:%M:%S", :strftime) %></td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>

      <div class="mt-6 flex items-center justify-end gap-x-6">
        <button type="submit" phx-click="save" phx-target={@myself} phx-value-action="97"
          class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500">
          Approve
        </button>

        <button type="submit" phx-click="save" phx-target={@myself} phx-value-action="96"
          class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500">
          Reject
        </button>
      </div>
    </div>
  </div>
</.form>







