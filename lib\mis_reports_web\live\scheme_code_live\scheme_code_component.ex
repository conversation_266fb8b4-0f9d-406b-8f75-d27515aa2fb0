defmodule MisReportsWeb.LoanSchemeCodeLive.LoanSchemeCodeComponent do
  use MisReportsWeb, :live_component
  use PipeTo.Override
  alias MisReports.{Repo}
  alias MisReports.{Prudentials, Prudentials.LoanSchemeCodes}
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  require Logger

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.SchemeCodeView , "loan_scheme_code.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.SchemeCodeView , "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{loan_scheme_code: loan_scheme_code} = assigns, socket) do
    changeset = Prudentials.change_loan_scheme_code(loan_scheme_code)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"loan_scheme_code" => loan_scheme_code}, socket) do
    changeset =
      socket.assigns.loan_scheme_code
      |> LoanSchemeCodes.changeset(loan_scheme_code)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"loan_scheme_code" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_save(socket, :reject, params)
      "97" -> handle_save(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    current_user_id = to_string(socket.assigns.current_user.id)
    user_id = socket.assigns.current_user.id
    audit_msg = "Created Scheme Code #{params["scheme_code_description"]} Successfully!"

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:loan_scheme_code, LoanSchemeCodes.changeset(%LoanSchemeCodes{maker_id: user_id}, params))
    |> UserController.audit_log(user_id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{loan_scheme_code: loan_scheme_code}} ->
        Task.start(fn ->
          case MisReports.Workflow.call_workflow(
            socket.assigns.reference,
            socket.assigns.process_id,
            current_user_id,
            80,
            "",
            "",
            "Submission of Scheme Code Creation"
          ) do
            {:ok, reference_number} ->
              Prudentials.update_loan_scheme_code(loan_scheme_code, %{reference: reference_number})
            {:error, reason} ->
              Logger.error("""
              [SchemeCodeComponent] Workflow update failed:
              Scheme Code ID: #{loan_scheme_code.id}
              Process ID: #{socket.assigns.process_id}
              Reference: #{socket.assigns.reference}
              Error: #{inspect(reason)}
              """)
              nil
          end
        end)

        # Return success immediately
        {:noreply,
         socket
         |> put_flash(:info, "Scheme Code created successfully")
         |> push_redirect(to: Routes.loan_scheme_code_index_path(socket, :new))}


         {:error, changeset} ->
          {:noreply, assign(socket, :changeset, changeset)}
      end
    end

  def handle_save(socket, :edit, params) do
    loan_scheme_code = socket.assigns.loan_scheme_code
    socket
    |> handle_update(params, loan_scheme_code)
    |> case do
      {:ok, loan_scheme_code} ->
        {:noreply,
          socket
          |> put_flash(:info, "Scheme Code updated successfully")
          |> push_redirect(to: Routes.loan_scheme_code_index_path(socket, :edit, loan_scheme_code))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_save(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Scheme Code Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Scheme Code rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject Scheme Code: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :approve, params) do
    loan_scheme_code = socket.assigns.loan_scheme_code
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Scheme Code Approval"


    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      LoanSchemeCodes.changeset(loan_scheme_code, %{
        status: "A",
        checker_id: current_user.id,
        checker_date: NaiveDateTime.utc_now()
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_loan_scheme_code}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Scheme Code approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Scheme Code approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve Scheme Code")
         |> assign(:changeset, %{loan_scheme_code.changeset | errors: failed_value.errors})}
    end
  end

  def handle_update(socket, params, loan_scheme_code) do
    audit_msg = "Updated Scheme Code #{params["scheme_code_description"]}"

    Ecto.Multi.new()
    |> Ecto.Multi.update(:loan_scheme_code, LoanSchemeCodes.changeset(loan_scheme_code, Map.merge(params, %{"status" => "D", "checker" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{loan_scheme_code: loan_scheme_code, audit_log: _user_log}} ->
        {:ok, loan_scheme_code}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

end
