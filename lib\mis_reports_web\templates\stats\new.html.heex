
<.form :let={f} for={@changeset} as={:stat} id="stats-form" phx-submit="update" phx-target={@myself}>

<div class="px-4 py-5 sm:p-6">
      <div class="space-y-12">
         <div class="border-b border-gray-900/10 pb-12">
            <div class="mt-8 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
               <div class="sm:col-span-2">
                  <div class="mt-2">
                     <%= text_input(
                        f,
                        :descript,
                        autocomplete: "description",
                        placeholder: "Description",
                        class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                        required: "true"
                        ) %> <%= error_tag(f, :descript) %>
                  </div>
               </div>
               
            </div>
         </div>
      </div>
   </div>
<div x-data="{ activeTab: 'tab1' }">
   <!-- Tab Buttons -->
   <div class="tabs">
      <nav class="flex" aria-label="Breadcrumb">
         <ol role="list" class="flex space-x-4 rounded-md bg-white px-6 shadow">
            <li class="flex">
               <div class="flex items-center">
                  <a href="#" class="text-gray-400 hover:text-gray-500">
                     <svg class="h-5 w-5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M9.293 2.293a1 1 0 011.414 0l7 7A1 1 0 0117 11h-1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-3a1 1 0 00-1-1H9a1 1 0 00-1 1v3a1 1 0 01-1 1H5a1 1 0 01-1-1v-6H3a1 1 0 01-.707-1.707l7-7z" clip-rule="evenodd" />
                     </svg>
                     <span class="sr-only">Home</span>
                  </a>
               </div>
            </li>
            <div>
            <li class="flex" >
               <button class="flex items-center { 'active': activeTab === 'tab1' }"  @click="activeTab = 'tab1'" >
                  <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                     <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                  </svg>
                  <a href="#" x-bind:class="{'text-blue-500' : activeTab === 'tab1'}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" phx-click="province_value" phx-value-province="lusaka" phx-target={@myself}>Lusaka </a>
               </button>
            </li>
         </div>
            <li class="flex">
               <div class="flex items-center { 'active': activeTab === 'tab2' }"  @click="activeTab = 'tab2'">
                  <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                     <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                  </svg>
                  <a href="#" x-bind:class="{'text-blue-500' : activeTab === 'tab2'}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" phx-click="province_value" phx-value-province="southern" phx-target={@myself} aria-current="page">S/Province</a>
               </div>
            </li>
            <li class="flex">
               <div class="flex items-center { 'active': activeTab === 'tab3' }"  @click="activeTab = 'tab3'">
                  <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                     <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                  </svg>
                  <a href="#" x-bind:class="{'text-blue-500' : activeTab === 'tab3'}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" phx-click="province_value" phx-value-province="central" phx-target={@myself} aria-current="page">Central  </a>
               </div>
            </li>
            <li class="flex">
               <div class="flex items-center { 'active': activeTab === 'tab4' }"  @click="activeTab = 'tab4'">
                  <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                     <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                  </svg>
                  <a href="#" x-bind:class="{'text-blue-500' : activeTab === 'tab4'}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" phx-click="province_value" phx-value-province="eastern" phx-target={@myself} aria-current="page">Eastern  </a>
               </div>
            </li>
            <li class="flex">
               <div class="flex items-center { 'active': activeTab === 'tab5' }"  @click="activeTab = 'tab5'">
                  <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                     <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                  </svg>
                  <a href="#" x-bind:class="{'text-blue-500' : activeTab === 'tab5'}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" phx-click="province_value" phx-value-province="western" phx-target={@myself}  aria-current="page">Western </a>
               </div>
            </li>
            <li class="flex">
               <div class="flex items-center { 'active': activeTab === 'tab6' }"  @click="activeTab = 'tab6'">
                  <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                     <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                  </svg>
                  <a href="#" x-bind:class="{'text-blue-500' : activeTab === 'tab6'}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" phx-click="province_value" phx-value-province="luapula" phx-target={@myself} aria-current="page">Luapula  </a>
               </div>
            </li>
            <li class="flex">
               <div class="flex items-center { 'active': activeTab === 'tab7' }"  @click="activeTab = 'tab7'" >
                  <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                     <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                  </svg>
                  <a href="#" x-bind:class="{'text-blue-500' : activeTab === 'tab7'}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" phx-click="province_value" phx-value-province="north_west" phx-target={@myself} aria-current="page">North Western  </a>
               </div>
            </li>
      
            <li class="flex">
               <div class="flex items-center { 'active': activeTab === 'tab8' }"  @click="activeTab = 'tab8'">
                  <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                     <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                  </svg>
                  <a href="#" x-bind:class="{'text-blue-500' : activeTab === 'tab8'}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" phx-click="province_value" phx-value-province="muchinga" phx-target={@myself} aria-current="page">Muchinga province </a>
               </div>
            </li>

            <li class="flex">
               <div class="flex items-center { 'active': activeTab === 'tab9' }"  @click="activeTab = 'tab9'">
                  <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                     <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                  </svg>
                  <a href="#" x-bind:class="{'text-blue-500' : activeTab === 'tab9'}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" phx-click="province_value" phx-value-province="northern" phx-target={@myself} aria-current="page">N/ Province </a>
               </div>
            </li>
         </ol>
      </nav>
   </div>
   <!-- Tab Content  Lusaka province-->
   <!--<div class="tab-content">
         <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white  shadow mt-2 overflow-x-auto ">
            <div class="px-4 py-5 sm:p-6">
               <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"><%= @province %> </a> 
               <table id="me" class="table table-bordered">
                  <thead>
                     <th></th>
                     <th>Zambians Males</th>
                     <th>Zambians Females</th>
                     <th>Non Zambian Males</th>
                     <th>Non Zambian Females</th>
                  </thead>
                  <tbody>
                     <tr>
                        <td>Executive Management</td>
                        <td><input name="exec_mng[zm]" type="number" value={@values && @values.exec_mng.zm} required="required" min="0"></td>
                        <td><input name="exec_mng[zf]" type="number" value={@values && @values.exec_mng.zf} required="required" min="0"></td>
                        <td><input name="exec_mng[nzm]" type="number" value={@values && @values.exec_mng.nzm} required="required" min="0"></td>
                        <td><input name="exec_mng[nzf]" type="number" value={@values && @values.exec_mng.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Senior Managers</td>
                        <td><input name="snr_mng[zm]" type="number" value={@values && @values.snr_mng.zm} required="required" min="0"></td>
                        <td><input name="snr_mng[zf]" type="number" value={@values && @values.snr_mng.zf} required="required" min="0"></td>
                        <td><input name="snr_mng[nzm]" type="number" value={@values && @values.snr_mng.nzm} required="required" min="0"></td>
                        <td><input name="snr_mng[nzf]" type="number" value={@values && @values.snr_mng.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Managers</td>
                        <td><input name="mng[zm]" type="number" value={@values && @values.mng.zm} required="required" min="0"></td>
                        <td><input name="mng[zf]" type="number" value={@values && @values.mng.zf} required="required" min="0"></td>
                        <td><input name="mng[nzm]" type="number" value={@values && @values.mng.nzm} required="required" min="0"></td>
                        <td><input name="mng[nzf]" type="number" value={@values && @values.mng.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Other Staff</td>
                        <td><input name="other_staff[zm]" type="number" value={@values && @values.other_staff.zm} required="required" min="0"></td>
                        <td><input name="other_staff[zf]" type="number" value={@values && @values.other_staff.zf} required="required" min="0"></td>
                        <td><input name="other_staff[nzm]" type="number" value={@values && @values.other_staff.nzm} required="required" min="0"></td>
                        <td><input name="other_staff[nzf]" type="number" value={@values && @values.other_staff.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>clerical staff</td>
                        <td><input name="cler_staff[zm]" type="number" value={@values && @values.cler_staff.zm} required="required" min="0"></td>
                        <td><input name="cler_staff[zf]" type="number" value={@values && @values.cler_staff.zf} required="required" min="0"></td>
                        <td><input name="cler_staff[nzm]" type="number" value={@values && @values.cler_staff.nzm} required="required" min="0"></td>
                        <td><input name="cler_staff[nzf]" type="number" value={@values && @values.cler_staff.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td colspan="5"></td>
                     </tr>
                     <tr>
                        <td>Chief Executive's Office</td>
                        <td><input name="ceo[zm]" type="number" value={@values && @values.ceo.zm} required="required" min="0"></td>
                        <td><input name="ceo[zf]" type="number" value={@values && @values.ceo.zf} required="required" min="0"></td>
                        <td><input name="ceo[nzm]" type="number" value={@values && @values.ceo.nzm} required="required" min="0"></td>
                        <td><input name="ceo[nzf]" type="number" value={@values && @values.ceo.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Credit</td>
                        <td><input name="cdt[zm]" type="number" value={@values && @values.cdt.zm} required="required" min="0"></td>
                        <td><input name="cdt[zf]" type="number" value={@values && @values.cdt.zf} required="required" min="0"></td>
                        <td><input name="cdt[nzm]" type="number" value={@values && @values.cdt.nzm} required="required" min="0"></td>
                        <td><input name="cdt[nzf]" type="number" value={@values && @values.cdt.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Treasury</td>
                        <td><input name="treas[zm]" type="number" value={@values && @values.treas.zm} required="required" min="0"></td>
                        <td><input name="treas[zf]" type="number" value={@values && @values.treas.zf} required="required" min="0"></td>
                        <td><input name="treas[nzm]" type="number" value={@values && @values.treas.nzm} required="required" min="0"></td>
                        <td><input name="treas[nzf]" type="number" value={@values && @values.treas.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Finance</td>
                        <td><input name="fin[zm]" type="number" value={@values && @values.fin.zm} required="required" min="0"></td>
                        <td><input name="fin[zf]" type="number" value={@values && @values.fin.zf} required="required" min="0"></td>
                        <td><input name="fin[nzm]" type="number" value={@values && @values.fin.nzm} required="required" min="0"></td>
                        <td><input name="fin[nzf]" type="number" value={@values && @values.fin.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Risk and Compliance</td>
                        <td><input name="risk[zm]" type="number" value={@values && @values.risk.zm} required="required" min="0"></td>
                        <td><input name="risk[zf]" type="number" value={@values && @values.risk.zf} required="required" min="0"></td>
                        <td><input name="risk[nzm]" type="number" value={@values && @values.risk.nzm} required="required" min="0"></td>
                        <td><input name="risk[nzf]" type="number" value={@values && @values.risk.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Operations</td>
                        <td><input name="op[zm]" type="number" value={@values && @values.op.zm} required="required" min="0"></td>
                        <td><input name="op[zf]" type="number" value={@values && @values.op.zf} required="required" min="0"></td>
                        <td><input name="op[nzm]" type="number" value={@values && @values.op.nzm} required="required" min="0"></td>
                        <td><input name="op[nzf]" type="number" value={@values && @values.op.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Infromation Technology</td>
                        <td><input name="ict[zm]" type="number" value={@values && @values.ict.zm} required="required" min="0"></td>
                        <td><input name="ict[zf]" type="number" value={@values && @values.ict.zf} required="required" min="0"></td>
                        <td><input name="ict[nzm]" type="number" value={@values && @values.ict.nzm} required="required" min="0"></td>
                        <td><input name="ict[nzf]" type="number" value={@values && @values.ict.nzf} required="required" min="0"></td>
                     </tr>
                     <tr>
                        <td>Others</td>
                        <td><input name="other[zm]" type="number" value={@values && @values.other.zm} required="required" min="0"></td>
                        <td><input name="other[zf]" type="number" value={@values && @values.other.zf} required="required" min="0"></td>
                        <td><input name="other[nzm]" type="number" value={@values && @values.other.nzm} required="required" min="0"></td>
                        <td><input name="other[nzf]" type="number" value={@values && @values.other.nzf} required="required" min="0"></td>
                     </tr>
                  </tbody>
               </table>
               <div class="mt-6 flex items-center gap-x-6">
                  <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
               </div>
            </div>
         </div>
   </div>-->
   <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white  shadow mt-2 overflow-x-auto" >
      <%= if f.data.status in ["PROGRESSING", "ACTIVE" ,"A" , "D"] do %>
      <input type="hidden"  name="province" value={@province}>
      <div class="px-4 py-5 sm:p-6">
         <div class="space-y-12">
            <nav class="flex" style="display:none">
               <ol role="list" class="flex space-x-4 rounded-md bg-white px-6 shadow">
                  <li class="flex" @click="activeTab = 0" class="activeTab === 0 ? 'tab-control active' : 'tab-control'">
                     <div class="flex items-center">
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                           <%= if @province == :cb do %>
                           <h2>Copperbelt </h2>
                           <% end %>
                        </a>
                     </div>
                  </li>
                  <li class="flex"  @click="activeTab = 1" class="activeTab === 1 ? 'tab-control active' : 'tab-control'">
                     <div class="flex items-center">
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#"  class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" aria-current="page">
                           <%= if @province == :lusaka do %>
                           <h2>Lusaka </h2>
                           <% end %>
                        </a>
                     </div>
                  </li>
                  <li class="flex" @click="activeTab = 2" class="activeTab === 2 ? 'tab-control active' : 'tab-control'">
                     <div class="flex items-center">
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" aria-current="page" >
                           <%= if @province == :southern do %>
                           <h2>South </h2>
                           <% end %>
                        </a>
                     </div>
                  </li>
                  <li class="flex" @click="activeTab = 3" class="activeTab === 3 ? 'tab-control active' : 'tab-control'">
                     <div class="flex items-center">
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" aria-current="page">
                           <%= if @province == :central do %>
                           <h2>Central </h2>
                           <% end %>
                        </a>
                     </div>
                  </li>
                  <li class="flex" @click="activeTab = 4" class="activeTab === 4 ? 'tab-control active' : 'tab-control'">
                     <div class="flex items-center">
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" aria-current="page">
                           <%= if @province == :eastern do %>
                           <h2>Eastern  </h2>
                           <% end %>
                        </a>
                     </div>
                  </li>
                  <li class="flex" @click="activeTab = 5" class="activeTab === 5 ? 'tab-control active' : 'tab-control'">
                     <div class="flex items-center">
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" aria-current="page">
                           <%= if @province == :western do %>
                           <h2>Western </h2>
                           <% end %>
                        </a>
                     </div>
                  </li>
                  <li class="flex" @click="activeTab = 6" class="activeTab === 6 ? 'tab-control active' : 'tab-control'">
                     <div class="flex items-center">
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" aria-current="page">
                           <div style="display:show;">
                              <%= if @province == :luapula do %>
                              <h2>Luapula </h2>
                              <% end %>
                           </div>
                        </a>
                     </div>
                  </li>
                  <li class="flex" @click="activeTab = 7" class="activeTab === 7 ? 'tab-control active' : 'tab-control'">
                     <div class="flex items-center">
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" aria-current="page">
                           <%= if @province == :north_west do %>
                           <h2>North Western </h2>
                           <% end %>
                        </a>
                     </div>
                  </li>
                  <li class="flex" @click="activeTab = 8" class="activeTab === 8 ? 'tab-control active' : 'tab-control'">
                     <div class="flex items-center" >
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" aria-current="page">
                           <%= if @province == :muchinga do %>
                           <h2>Muchinga </h2>
                           <% end %> 
                        </a>
                     </div>
                  </li>
                  <li class="flex" @click="activeTab = 9" class="activeTab === 9 ? 'tab-control active' : 'tab-control'" >
                     <div class="flex items-center">
                        <svg class="h-full w-6 flex-shrink-0 text-gray-200" viewBox="0 0 24 44" preserveAspectRatio="none" fill="currentColor" aria-hidden="true">
                           <path d="M.293 0l22 22-22 22h1.414l22-22-22-22H.293z" />
                        </svg>
                        <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" aria-current="page">
                           <%= if @province == :muchinga do %>
                           <h2>Muchinga </h2>
                           <% end %> 
                        </a>
                     </div>
                  </li>
               </ol>
            </nav>
            <table id="me" class="table table-bordered">
               <a href="#" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                  <h2><%= @province %> </h2>    
               </a>
               <thead>
                  <th></th>
                  <th>Zambians Males</th>
                  <th>Zambians Females</th>
                  <th>Non Zambian Males</th>
                  <th>Non Zambian Females</th>
               </thead>
               <tbody>
                  <tr>
                     <td>Executive Management</td>
                     <input type="hidden" name="province" value={@province}>
                     <td><input name="exec_mng[zm]" type="number" value={@values && @values.exec_mng.zm}  min="0"></td>
                     <td><input name="exec_mng[zf]" type="number" value={@values && @values.exec_mng.zf}  min="0"></td>
                     <td><input name="exec_mng[nzm]" type="number" value={@values && @values.exec_mng.nzm}  min="0"></td>
                     <td><input name="exec_mng[nzf]" type="number" value={@values && @values.exec_mng.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>Senior Managers</td>
                     <td><input name="snr_mng[zm]" type="number" value={@values && @values.snr_mng.zm}  min="0"></td>
                     <td><input name="snr_mng[zf]" type="number" value={@values && @values.snr_mng.zf}  min="0"></td>
                     <td><input name="snr_mng[nzm]" type="number" value={@values && @values.snr_mng.nzm}  min="0"></td>
                     <td><input name="snr_mng[nzf]" type="number" value={@values && @values.snr_mng.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>Managers</td>
                     <td><input name="mng[zm]" type="number" value={@values && @values.mng.zm}  min="0"></td>
                     <td><input name="mng[zf]" type="number" value={@values && @values.mng.zf}  min="0"></td>
                     <td><input name="mng[nzm]" type="number" value={@values && @values.mng.nzm}  min="0"></td>
                     <td><input name="mng[nzf]" type="number" value={@values && @values.mng.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>Other Staff</td>
                     <td><input name="other_staff[zm]" type="number" value={@values && @values.other_staff.zm}  min="0"></td>
                     <td><input name="other_staff[zf]" type="number" value={@values && @values.other_staff.zf}  min="0"></td>
                     <td><input name="other_staff[nzm]" type="number" value={@values && @values.other_staff.nzm}  min="0"></td>
                     <td><input name="other_staff[nzf]" type="number" value={@values && @values.other_staff.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>clerical staff</td>
                     <td><input name="cler_staff[zm]" type="number" value={@values && @values.cler_staff.zm}  min="0"></td>
                     <td><input name="cler_staff[zf]" type="number" value={@values && @values.cler_staff.zf}  min="0"></td>
                     <td><input name="cler_staff[nzm]" type="number" value={@values && @values.cler_staff.nzm}  min="0"></td>
                     <td><input name="cler_staff[nzf]" type="number" value={@values && @values.cler_staff.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td colspan="5"></td>
                  </tr>
                  <tr>
                     <td>Chief Executive's Office</td>
                     <td><input name="ceo[zm]" type="number" value={@values && @values.ceo.zm}  min="0"></td>
                     <td><input name="ceo[zf]" type="number" value={@values && @values.ceo.zf}  min="0"></td>
                     <td><input name="ceo[nzm]" type="number" value={@values && @values.ceo.nzm}  min="0"></td>
                     <td><input name="ceo[nzf]" type="number" value={@values && @values.ceo.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>Credit</td>
                     <td><input name="cdt[zm]" type="number" value={@values && @values.cdt.zm}  min="0"></td>
                     <td><input name="cdt[zf]" type="number" value={@values && @values.cdt.zf}  min="0"></td>
                     <td><input name="cdt[nzm]" type="number" value={@values && @values.cdt.nzm}  min="0"></td>
                     <td><input name="cdt[nzf]" type="number" value={@values && @values.cdt.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>Treasury</td>
                     <td><input name="treas[zm]" type="number" value={@values && @values.treas.zm}  min="0"></td>
                     <td><input name="treas[zf]" type="number" value={@values && @values.treas.zf}  min="0"></td>
                     <td><input name="treas[nzm]" type="number" value={@values && @values.treas.nzm}  min="0"></td>
                     <td><input name="treas[nzf]" type="number" value={@values && @values.treas.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>Finance</td>
                     <td><input name="fin[zm]" type="number" value={@values && @values.fin.zm}  min="0"></td>
                     <td><input name="fin[zf]" type="number" value={@values && @values.fin.zf}  min="0"></td>
                     <td><input name="fin[nzm]" type="number" value={@values && @values.fin.nzm}  min="0"></td>
                     <td><input name="fin[nzf]" type="number" value={@values && @values.fin.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>Risk and Compliance</td>
                     <td><input name="risk[zm]" type="number" value={@values && @values.risk.zm}  min="0"></td>
                     <td><input name="risk[zf]" type="number" value={@values && @values.risk.zf}  min="0"></td>
                     <td><input name="risk[nzm]" type="number" value={@values && @values.risk.nzm} min="0"></td>
                     <td><input name="risk[nzf]" type="number" value={@values && @values.risk.nzf} min="0"></td>
                  </tr>
                  <tr>
                     <td>Operations</td>
                     <td><input name="op[zm]" type="number" value={@values && @values.op.zm}  min="0"></td>
                     <td><input name="op[zf]" type="number" value={@values && @values.op.zf}  min="0"></td>
                     <td><input name="op[nzm]" type="number" value={@values && @values.op.nzm}  min="0"></td>
                     <td><input name="op[nzf]" type="number" value={@values && @values.op.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>Infromation Technology</td>
                     <td><input name="ict[zm]" type="number" value={@values && @values.ict.zm}  min="0"></td>
                     <td><input name="ict[zf]" type="number" value={@values && @values.ict.zf}  min="0"></td>
                     <td><input name="ict[nzm]" type="number" value={@values && @values.ict.nzm}  min="0"></td>
                     <td><input name="ict[nzf]" type="number" value={@values && @values.ict.nzf}  min="0"></td>
                  </tr>
                  <tr>
                     <td>Others</td>
                     <td><input name="other[zm]" type="number" value={@values && @values.other.zm}  min="0"></td>
                     <td><input name="other[zf]" type="number" value={@values && @values.other.zf}   min="0"></td>
                     <td><input name="other[nzm]" type="number" value={@values && @values.other.nzm}  min="0"></td>
                     <td><input name="other[nzf]" type="number" value={@values && @values.other.nzf}  min="0"></td>
                  </tr>
               </tbody>
            </table>
            <div class="mt-6 flex items-center gap-x-6">
               <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
            </div>
            <div>
                <button phx-click="new_action" phx-target={@myself} class="whitespace-nowrap py-2 px-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                    Proceed
                </button>
               </div>
         </div>
      </div>
      <% end %>
   </div>
</div>
</.form>


