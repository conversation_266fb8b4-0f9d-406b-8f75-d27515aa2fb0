<style>

  .ex1{
     transition: all 1.5s;
     animation: animation-name 0.5s linear;
  }
  
  @keyframes animation-name{
     from{
     transform: translateX(-100%);
     height: 0px;
     }
  }
  </style>

<.form :let={f} for={@changeset} as={:regulatory_capital} id="regulatory-capital-form" phx-submit="save" phx-change="validate" phx-target={@myself}>
    <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow ex1">
      <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
        <div class="-ml-4 -mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
          <div class="ml-4 mt-4">
            <div class="flex items-center">
              <span class="inline-block h-10 w-10 overflow-hidden rounded-full bg-gray-100">
                <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </span>
  
              <div class="ml-4">
                <h3  class="text-base font-semibold leading-6 text-gray-900">Create Regulatory Capital</h3>
                <p class="text-sm text-gray-500">
                  <a href="#">Regulatory Capital Details</a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <div class="px-4 py-5 sm:p-6">
        <div class="space-y-12">
          <div class="border-b border-gray-900/10 pb-12">
            <div class="mt-8 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
  
              <div class="sm:col-span-3">
                <label for="regulatory_capital" class="block text-sm font-medium leading-6 text-gray-900">Regulatory Capital</label>
                <div class="mt-2">
                  <%= text_input(
                    f,
                    :regulatory_capital,
                    autocomplete: "Regulatory Capital",
                    placeholder: "Regulatory Capital",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %> <%= error_tag(f, :regulatory_capital) %>
                </div>
              </div>

               <div class="sm:col-span-3">
                <label for="date" class="block text-sm font-medium leading-6 text-gray-900">Month</label>
                <div class="mt-2">
                  <%= text_input(
                    f,
                    :date,
                    autocomplete: "Month",
                    placeholder: "Month",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    type: "date",
                    disabled: "true"
                  ) %> <%= error_tag(f, :date) %>
                </div>
              </div>

            </div>
          </div>
        </div>
  
        <div class="mt-6 flex items-center justify-end gap-x-6">
        <button 
          type="submit" 
          phx-click="save"
          phx-target={@myself}
          phx-value-action="97"
          class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600">
          Approve
        </button>

        <button 
          type="submit"
          phx-click="save"
          phx-target={@myself}
          phx-value-action="96"
          class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
          Reject
        </button>
      </div>
      </div>
    </div>
</.form>
  