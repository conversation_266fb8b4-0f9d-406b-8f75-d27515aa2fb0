
   
    <style>
       
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background-color: white;
            padding: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1a5276;
            padding-bottom: 15px;
        }
        
        .header h1 {
            color: #1a5276;
            margin-bottom: 5px;
        }

       
        
        .header p {
            color: #566573;
            font-size: 14px;
        }
        
        .four-column {
            display: flex;
            gap: 3px;
            overflow-x: auto; /* Allow horizontal scrolling if needed */
        }
        
        .column {
            flex: 1;
            min-width: 100px; /* Ensure minimum width for readability */
        }
        
        .statement-section {
            margin-bottom: 20px;
            break-inside: avoid;
             padding: 8px 5px;
        }
        
        .statement-title {
            background-color: #1a5276;
            color: white;
            padding: 8px 5px;
            font-weight: bold;
            font-size: 9px;
            margin-bottom: 10px;
            white-space: nowrap;
        }
        .fonts_bold{
            font-weight:900;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
            font-size: 10px;
        }

         .chile{
             border-right: 1px solid #0070c0;
             height: 700px;
             border-bottom: 1px solid #0070c0;
               
              }


        .chiles{
        border-right: 1px solid #0070c0;
         height: 700px;
         border-bottom: 1px solid #0070c0;
        
        }

          .chilez{
        border-right: 1px solid #0070c0;
         height: 700px;
         border-bottom: 1px solid #0070c0;
        
        }

         .chilep{
       
         height: 700px;
         border-bottom: 1px solid #0070c0;
        
        }

        .fonts{
            font-size:10px;
            font-weight:600;
            text-align: right;
        }
        

        
        th, td {
            padding: 0px 0px;
            text-align: left;
           border-bottom: 1px solid #fff;
        }
        
        th {
            background-color: #fff;
            font-weight: bold;
            color: #2c3e50;
        }
        
        tr:nth-child(even) {
            background-color: #fff;
        }
        
        .amount {
            text-align: right;
        }
        
        .total-row {
            font-weight: bold;
            background-color: #fff;
        }
        
       
        .indent-1 {
            padding-left: 15px;
        }
        
        .indent-2 {
            padding-left: 25px;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #566573;
            text-align: center;
        }
        
        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        
        .signature {
            text-align: center;
            width: 45%;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            margin-bottom: 5px;
        }
        
        @media (max-width: 1200px) {
            .four-column {
                flex-wrap: wrap;
            }
            
            .column {
                flex: 1 1 45%;
            }
        }
        
        @media (max-width: 768px) {
            .four-column {
                flex-direction: column;
            }
            
            .column {
                flex: 1 1 100%;
            }
            
            table {
                font-size: 10px;
            }
        }
    </style>


 <a href="#" phx-click="save-report" phx-disable-with="Loading..." class="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Export Report</a>

 <button id="download-quarterly" phx-hook="DownloadQuarterly" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
      Download to PDF
  </button> 
<br><br>

    <div id="pdf-content"  class="container">
        <div>
         <img src="/images/011.png" alt="Probase" style="width: 20%; ">
            <div >
            <h1 style="text-align: right; font-weight:900">QUARTERLY FINANCIAL STATEMENTS - 31 MARCH 2025</h1>
             <p class="fonts">(Published in accordance with section 92(1) of the Banking and Financial Services Act 2017 as amended)</p>
            <p class="fonts">Note that the financial statements on a month-on-month basis are available on request at every branch of our bank.</p>
            </div>
        </div>
        
        <div class="four-column" >
            
            <!-- INCOME STATEMENT -->
            <div class="column">
                <div class="statement-section chile">
                    <div class="statement-title">INCOME STATEMENT FOR QUARTER <br> <%= Timex.format!(Date.from_iso8601!(@filter_params["end_date"]),"%e %B %Y", :strftime) %> </div>
                    <table>
                        <tr >
                            <th></th>
                            <th class="amount">Quarter to Date<br>ZMW' 000</th>
                             <th class="amount">Year to Date<br>ZMW' 000</th>
                        </tr>
                        <tr >
                            <td colspan="2" ><strong>Interest Income from:-</strong></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Loans and overdrafts</td>
                            <td class="amount" ><%= get_qtd(assigns, :loans_and_advances_from_normal_deposits) %> </td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters", :loans_and_advances_from_normal_deposits) %></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Banks and financial institutions</td>
                            <td class="amount" ><%= get_qtd(assigns, :from_banks_and_other_financial_institutions)  %></td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters", :from_banks_and_other_financial_institutions) %></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Securities</td>
                            <td class="amount" ><%= get_qtd(assigns, :securities) %></td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters", :securities) %></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Other</td>
                            <td class="amount" ><%= get_qtd_by_key(assigns, "C32") %></td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters_2", :C32) %></td>
                        </tr>
                        <tr class="subtotal-row">
                            <td class="fonts_bold">Total Interest Income</td>
                            <td class="amount fonts_bold"><%= get_qtd(assigns, :total_interest_income) %></td>
                            <td class="amount fonts_bold"><%= get_ytd_values(assigns, "sum_across_quarters", :total_interest_income) %></td>
                        </tr>
                        <tr  >
                            <td   colspan="2"><strong>Interest Expense:-</strong></td>
                        </tr>
                        <tr>
                            <td class="indent-1"  >Deposits</td>
                            <td class="amount"  >(<%= get_qtd(assigns, :deposits) %>)</td>
                            <td class="amount"  >(<%= get_ytd_values(assigns, "sum_across_quarters", :deposits) %>)</td>
                        </tr>
                        <tr>
                            <td class="indent-1"  >Subordinated debt</td>
                            <td class="amount"  >(<%= get_qtd_by_key(assigns, "C57") %>)</td>
                            <td class="amount"  >(<%= get_ytd_values(assigns, "sum_across_quarters_2", :C57) %>) </td>
                        </tr>
                        <tr>
                            <td class="indent-1"  >Paid to banks and financial institutions</td>
                            <td class="amount"  >(<%= get_qtd(assigns, :interest_paid_to_banks_and_financial_institutions) %>)  </td>
                            <td class="amount"  >(<%= get_ytd_values(assigns, "sum_across_quarters", :interest_paid_to_banks_and_financial_institutions) %>)</td>
                        </tr>
                        <tr>
                            <td class="indent-1"  >Other</td>
                            <td class="amount"  >(<%= get_qtd_by_key(assigns, "C59") %>)</td>
                            <td class="amount"  >(<%= get_ytd_values(assigns, "sum_across_quarters_2", :C59) %>)</td>
                        </tr>

                         <tr>
                            <td class="indent-1"  ></td>
                            <td class="amount"  >() </td>
                            <td class="amount"  >()</td>
                        </tr>
                        <tr>
                            <td>Total Interest Expense</td>
                            <td class="amount" >(<%= get_qtd(assigns, :total_interest_expense) %>)</td>
                            <td class="amount" >(<%= get_ytd_values(assigns, "sum_across_quarters", :total_interest_expense) %>) </td>
                        </tr>
                       

                        <tr class="subtotal-row">
                            <td>Net Interest Income </td>
                            <td class="amount">(<%= get_qtd(assigns, :net_interest_income) %>)</td>
                            <td class="amount"><%= get_ytd_values(assigns, "sum_across_quarters", :net_interest_income) %></td>
                        </tr>
                        <tr>
                            <td >Provision for loan losses</td>
                            <td class="amount" >(<%= get_qtd(assigns, :total_provisions) %>)</td>
                            <td class="amount" >(<%= get_ytd_values(assigns, "sum_across_quarters", :total_provisions) %>)</td>
                        </tr>
                        <tr>
                            <td>Net Interest Income after loan loss provision</td>
                            <td class="amount" >(<%= get_qtd(assigns, :net_interest_income_after_provisions) %>)</td>
                            <td class="amount" >(<%= get_ytd_values(assigns, "sum_across_quarters", :net_interest_income_after_provisions) %>) </td>
                        </tr>
                        <tr>
                            <td  colspan="2"><strong>Non interest income</strong></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Commissions, fees and service charges</td>
                            <td class="amount" ><%= get_qtd_by_key(assigns, "C71") %></td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters_2", :C71) %></td>
                        </tr>
                        <tr>
                            <td colspan="2" class="indent-1" ><strong>Foreign Exchange:-</strong></td>
                        </tr>
                        <tr>
                            <td class="indent-2" >Fees from foreign exchange transactions</td>
                            <td class="amount" ><%= get_qtd_by_key(assigns, "C73") %> </td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters_2", :C73) %></td>
                        </tr>
                        <tr>
                            <td class="indent-2" >Realised trading gains (losses)</td>
                            <td class="amount" ><%= get_qtd_by_key(assigns, "C77") %></td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters_2", :C77) %></td>
                        </tr>
                        <tr>
                            <td class="indent-2" >Unrealised trading gains (losses)</td>
                            <td class="amount" ><%= get_qtd_by_key(assigns, "C74") %></td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters_2", :C74) %></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Other income</td>
                            <td class="amount" ><%= get_qtd_by_key(assigns, "C80") %></td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters_2", :C80) %></td>
                        </tr>
                         <tr class="subtotal-row">
                            <td>Non interest income</td>
                            <td class="amount">(<%= get_qtd(assigns, :total_non_interest_income) %>)</td>
                            <td class="amount"> <%= get_ytd_values(assigns, "sum_across_quarters", :total_non_interest_income) %></td>
                        </tr>
                        
                        <tr class="subtotal-row">
                            <td>Net interest & other income</td>
                            <td class="amount">(<%= get_qtd(assigns, :net_interest_and_other_income) %>)</td>
                            <td class="amount"> <%= get_ytd_values(assigns, "sum_across_quarters", :net_interest_and_other_income) %> </td>
                        </tr>
                        
                        <tr>
                            <td  colspan="2"><strong>Non-interest expenses</strong></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Depreciation</td>
                            <td class="amount" >(<%= get_qtd_by_key(assigns, "C87") %>)</td>
                            <td class="amount" >(<%= get_ytd_values(assigns, "sum_across_quarters_2", :C87) %>)</td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Other expenses</td>
                            <td class="amount" >(<%= get_qtd_by_key(assigns, "C94") %>)</td>
                            <td class="amount" >(<%= get_ytd_values(assigns, "sum_across_quarters_2", :C94) %>)</td>
                        </tr>
                        <tr class="subtotal-row">
                            <td>Total non interest expenses</td>
                            <td class="amount">(<%= get_qtd(assigns, :total_non_interest_expenses) %>)</td>
                            <td class="amount">(<%= get_ytd_values(assigns, "sum_across_quarters", :total_non_interest_expenses) %>)</td>

                        </tr>
                        <tr>
                            <td >Income before taxes</td>
                            <td class="amount" ><%= get_qtd(assigns, :income_loss_before_taxes) %></td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters", :income_loss_before_taxes) %></td>
                        </tr>
                        <tr>
                            <td >Taxation</td>
                            <td class="amount" >(<%= get_qtd_by_key(assigns, "C97") %>)</td>
                            <td class="amount" >(<%= get_ytd_values(assigns, "sum_across_quarters_2", :C97) %>)</td>
                        </tr>
                        <tr>
                            <td >Income after taxes</td>
                            <td class="amount" >(<%= get_qtd(assigns, :income_loss_after_taxes) %>)</td>
                            <td class="amount" >(<%= get_ytd_values(assigns, "sum_across_quarters", :income_loss_after_taxes) %>)</td>
                        </tr>
                        <tr class="total-row" >
                            <td >Divided</td>
                            <td class="amount" ></td>
                            <td class="amount" ></td>
                        </tr>

                        <tr class="total-row" >
                            <td >Net income</td>
                            <td class="amount" >(<%= get_qtd(assigns, :income_loss_after_taxes) %>)</td>
                            <td class="amount" ><%= get_ytd_values(assigns, "sum_across_quarters", :income_loss_after_taxes) %></td>
                        </tr>
                    </table>
                </div>
            </div>

        <!-- BALANCE SHEET -->
           
             <div class="column">
                <div class="statement-section chiles">
                    <div class="statement-title">BALANCE SHEET <br> <%= Timex.format!(Date.from_iso8601!(@filter_params["end_date"]),"%e %B %Y", :strftime) %></div>
                    <table>
                        <tr>
                            <th>ASSETS</th>
                            <th class="amount">ZMW' 000</th>
                        </tr>
                        <tr>
                            <td>Notes and Coins</td>
                            <td class="amount"><%= if get_in(@data, [:blc_sheet, "B11"]), do: get_in(@data, [:blc_sheet, "B11"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Balances with Bank of Zambia</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B16"]), do: get_in(@data, [:blc_sheet, "B16"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Balances with Banks and other financial institutions in Zambia</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B22"]), do: get_in(@data, [:blc_sheet, "B22"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Balances with Banks and other financial institutions abroad</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B26"]), do: get_in(@data, [:blc_sheet, "B26"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Investments in Securities</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B29"]), do: get_in(@data, [:blc_sheet, "B29"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Loans and Advances(Net)</td>
                            <td class="amount" ><%= if @data["B42"], do: @data["B42"], else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Interbranch balances</td>
                            <td class="amount" >-</td>
                        </tr>
                        <tr >
                            <td >Fixed Assets (Net book value)</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B49"]), do: get_in(@data, [:blc_sheet, "B49"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Other Assets</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B50"]), do: get_in(@data, [:blc_sheet, "B50"]), else: "-" %></td>
                        </tr>
                        <tr class="total-row">
                            <td>Total Assets</td>
                            <%= if get_in(@data, [:blc_sheet, "B60"]) == get_in(@data, [:blc_sheet, "B150"])do %>
                            <td class="amount"><%= if get_in(@data, [:blc_sheet, "A60"]), do: get_in(@data, [:blc_sheet, "A60"]), else: "-" %></td>
                            <% else %>
                            <td class="amount"><%= if get_in(@data, [:blc_sheet, "B60"]), do: get_in(@data, [:blc_sheet, "B60"]), else: "-" %></td>
                            <% end %>
                        </tr>
                    </table>
                    
                    <table>
                        <tr>
                            <th >LIABILITIES</th>
                            <th class="amount" >ZMW' 000</th>
                        </tr>
                        <tr >
                            <td >Deposits</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B63"]), do: get_in(@data, [:blc_sheet, "B63"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Balances due to Bank of Zambia</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B67"]), do: get_in(@data, [:blc_sheet, "B67"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Balances due to Banks and other financial institutions in Zambia</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B72"]), do: get_in(@data, [:blc_sheet, "B72"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Balances due to Banks and other financial institutions abroad</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B76"]), do: get_in(@data, [:blc_sheet, "B76"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Other Liabilities</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B81"]), do: get_in(@data, [:blc_sheet, "B81"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Other borrowed funds</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B91"]), do: get_in(@data, [:blc_sheet, "B91"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td >Shareholders' equity</td>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B96"]), do: get_in(@data, [:blc_sheet, "B96"]), else: "-" %></td>
                        </tr>
                        <tr class="total-row">
                            <td >Total liabilities and shareholders' equity</td>
                            <%= if get_in(@data, [:blc_sheet, "B105"]) == get_in(@data, [:blc_sheet, "B60"])do %>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B105"]), do: get_in(@data, [:blc_sheet, "B105"]), else: "-" %></td>
                            <% else %>
                            <td class="amount" ><%= if get_in(@data, [:blc_sheet, "B105"]), do: get_in(@data, [:blc_sheet, "B105"]), else: "-" %></td>
                            <% end %>
                        </tr>
                    </table>
                    
                    <table>
                        <tr>
                            <td>Contingent liabilities</td>
                            <td class="amount"><%= if get_in(@data, [:blc_sheet, "B108"]), do: get_in(@data, [:blc_sheet, "B108"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td>Commitments</td>
                            <td class="amount"><%= if get_in(@data, [:blc_sheet, "B117"]), do: get_in(@data, [:blc_sheet, "B117"]), else: "-" %></td>
                        </tr>
                       
                        
                    </table>
                </div>
            </div>
            
            
            
            
            <!-- STATEMENT OF CAPITAL POSITION -->
            <div class="column">
                <div class="statement-section chilez">
                    <div class="statement-title">STATEMENT OF CAPITAL POSITION  <br>  <%= Timex.format!(Date.from_iso8601!(@filter_params["end_date"]),"%e %B %Y", :strftime) %></div>
                    <table>
                        <tr>
                            <th></th>
                            <th class="amount">ZMW' 000</th>
                        </tr>
                        <tr>
                            <td  colspan="2"><strong>1. Primary Capital (Tier 1)</strong></td>
                        </tr>
                        <tr >
                            <td class="indent-1" >Paid-up common shares</td>
                            <td class="amount" ><%= if get_in(@data, [:sh15, "C21"]), do: get_in(@data, [:sh15, "C21"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Share premium</td>
                            <td class="amount" >-</td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Retained earnings</td>
                            <td class="amount" ><%= if get_in(@data, [:sh15, "C24"]), do: get_in(@data, [:sh15, "C24"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >General Reserve</td>
                            <td class="amount" ><%= if get_in(@data, [:sh15, "C35"]), do: get_in(@data, [:sh15, "C35"]), else: "-" %></td>
                        </tr>
                        <tr >
                            <td class="indent-1" >Statutory Reserves</td>
                            <td class="amount" ><%= if get_in(@data, [:sh15, "C26"]), do: get_in(@data, [:sh15, "C26"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-1" >Sub total</td>
                            <td class="amount" ><%= if get_in(@data, [:sh15, "C28"]), do: get_in(@data, [:sh15, "C28"]), else: "-" %></td>
                        </tr>
                        <tr> 
                            <td class="indent-1" >Other adjustments</td>
                            <td class="amount">(<%= if get_in(@data, [:sh15, "C25"]), do: get_in(@data, [:sh15, "C25"]), else: "-" %>)</td>
                        </tr>
                        <tr class="subtotal-row" >
                            <td >Total Primary Capital</td>
                            <td class="amount" ><%= if get_in(@data, [:sh15, "C47"]), do: get_in(@data, [:sh15, "C47"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td colspan="2"><strong>2. Secondary Capital (Tier 2)</strong></td>
                        </tr>
                        <tr>
                            <td class="indent-1">Eligible subordinated term debt</td>
                            <td class="amount"><%= if get_in(@data, [:sh15, "C50"]), do: get_in(@data, [:sh15, "C50"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-1">Revaluation Reserve (40% max)</td>
                            <td class="amount"><%= if get_in(@data, [:sh15, "C53"]), do: get_in(@data, [:sh15, "C53"]), else: "-" %></td>
                        </tr>
                        <tr class="subtotal-row">
                            <td>Eligible Secondary Capital</td>
                            <td class="amount"><%= if get_in(@data, [:sh15, "C55"]), do: get_in(@data, [:sh15, "C55"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td><strong>3. Eligible Total Capital (1+2)</strong></td>
                            <td class="amount"><%= if get_in(@data, [:sh15, "C61"]), do: get_in(@data, [:sh15, "C61"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td><strong>4. Minimum Total Capital Requirement</strong></td>
                            <td class="amount"><%= if get_in(@data, [:sh15, "C58"]), do: get_in(@data, [:sh15, "C58"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td><strong>5. Excess (Deficiency)</strong></td>
                            <td class="amount"><%= if get_in(@data, [:sh15, "C60"]), do: get_in(@data, [:sh15, "C60"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td><strong>6. Risk Based Assets</strong></td>
                            <td class="amount"><%= if get_in(@data, [:sh15, "C62"]), do: get_in(@data, [:sh15, "C62"]), else: "-" %></td>
                        </tr>
                        
                    </table>
                </div>
            </div>


            <!-- STATEMENT OF LIQUIDITY POSITION -->
            <div class="column">
                <div class="statement-section chilep">
                    <div class="statement-title">STATEMENT OF LIQUIDITY POSITION  <br>  <%= Timex.format!(Date.from_iso8601!(@filter_params["end_date"]),"%e %B %Y", :strftime) %></div>
                    <table>
                        <tr>
                            <th></th>
                            <th class="amount">ZMW' 000</th>
                        </tr>
                        <tr>
                            <td colspan="2"><strong>(i) DEPOSIT LIABILITIES AND BILLS PAYABLE</strong></td>
                        </tr>
                        <tr>
                            <td class="indent-1">a Demand Deposits</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "B64"]), do: get_in(@data, [:liquid_position, "B64"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-1">b Savings Deposits</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "B65"]), do: get_in(@data, [:liquid_position, "B65"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-1">c Time Deposits</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "B66"]), do: get_in(@data, [:liquid_position, "B66"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-1">d Bills Payable</td>
                            <td class="amount">-</td>
                        </tr>
                        <tr class="subtotal-row">
                            <td>TOTAL DEPOSIT LIABILITIES AND BILLS PAYABLE</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "B63"]), do: get_in(@data, [:liquid_position, "B63"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td>(ii) TOTAL DEPOSIT LIABILITIES AND BILLS PAYABLE AT THE END OF THE PREVIOUS QUARTER</td>
                            <td class="amount"></td>
                        </tr>
                        <tr>
                            <td colspan="2"><strong>(iii) LIQUID ASSETS</strong></td>
                        </tr>
                        <tr>
                            <td class="indent-1">1. Notes and coins</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "B11"]), do: get_in(@data, [:liquid_position, "B11"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-1">2. Balances with Bank of Zambia</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="indent-2">a Current account</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "B19"]), do: get_in(@data, [:liquid_position, "B19"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-2">b Statutory deposit accounts</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "statutory"]), do: get_in(@data, [:liquid_position, "statutory"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-2">c OMO deposits</td>
                            <td class="amount">-</td>
                        </tr>
                        <tr>
                            <td class="indent-2">d Other balances</td>
                            <td class="amount">-</td>
                        </tr>
                        <tr>
                            <td class="indent-1">3. Treasury Bills issued by the Government</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "Treasury_bills"]), do: get_in(@data, [:liquid_position, "Treasury_bills"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td class="indent-1">4. Money at call with other banks</td>
                            <td class="amount">-</td>
                        </tr>
                        <tr>
                            <td class="indent-1">5. Bills of exchange and promissory notes</td>
                            <td class="amount">-</td>
                        </tr>
                        <tr>
                            <td class="indent-1">6. Local registered securities</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "local_registered_securities"]), do: get_in(@data, [:liquid_position, "local_registered_securities"]), else: "-" %> </td>
                        </tr>
                        <tr>
                            <td class="indent-1">7. Items in transit between banks and branches</td>
                            <td class="amount">-</td>
                        </tr>
                        <tr class="total-row">
                            <td>TOTAL LIQUID ASSETS</td>
                            <td class="amount"><%= if get_in(@data, [:liquid_position, "TOTAL_ASSETS"]), do: get_in(@data, [:liquid_position, "TOTAL_ASSETS"]), else: "-" %></td>
                        </tr>
                        <tr>
                            <td colspan="2"><strong>iv - RATIOS</strong></td>
                        </tr>
                        <%
                            
                                 number = fn str ->
                                 case str do
                                    nil -> 0.0
                                    "-" -> 0.0
                                    str when is_binary(str) ->
                                       str = String.replace(str, ~r/[^0-9.-]/, "")
                                       case String.contains?(str, ".") do
                                       true -> String.to_float(str)
                                       false -> String.to_integer(str) / 1.0
                                       end
                                    num when is_number(num) -> num / 1.0
                                    _ -> 0.0
                                 end
                                 end

                                 # Calculate the liquid assets percentage
                                 liquid_assets = (
                                 number.(get_in(@data, [:liquid_position, "B11"])) +
                                 number.(get_in(@data, [:liquid_position, "B19"])) +
                                 number.(get_in(@data, [:liquid_position, "Treasury_bills"]))
                                 )
                                 total_deposits = max(number.(get_in(@data, [:liquid_position, "B63"])), 1.0)
                                 liquid_assets_percentage = Float.round(liquid_assets / total_deposits * 100, 1)
                                 %>
                        <tr>
                            <td class="indent-1">1. Liquid assets as a percentage of total deposit liabilities</td>
                            <td class="amount"><%= liquid_assets_percentage %>%</td>
                        </tr>
                        <%
                              total_assets = number.(get_in(@data, [:liquid_position, "TOTAL_ASSETS"]))
                              total_deposits = max(number.(get_in(@data, [:liquid_position, "B63"])), 1)
                              total_liquid_assets_percentage = Float.round(total_assets / total_deposits * 100, 1)
                              %>
                        <tr>
                            <td class="indent-1">2. Total Liquid assets as a percentage total deposit liabilities</td>
                            <td class="amount"><%= total_liquid_assets_percentage %>%</td>
                        </tr>
                        <tr>
                            <td class="indent-1">3. Total Liquid assets as a percentage of previous quarter</td>
                            <td class="amount"></td>
                        </tr>
                       
                        
                    </table>
                </div>
            </div>
        </div>
        
        <div>

        <div class="grid grid-cols-4 gap-2">
            <div class="col-span-2"><p style="font-size:9px; font-weight:300">THE ABOVE INFORMATION IS UNAUDITED BUT IS IN AGREEMENT WITH THE FINANCIAL STATEMENTS SUBMITTED TO BANK OF ZAMBIA</p></div>
            <!-- ... -->
            <div style="font-size:9px;  font-weight:800"> Mwindwa Siakalima<br><p style="font-size:9px;  font-weight:300">Chief Executive</p></div>
            <div style="font-size:9px;  font-weight:800">Mizimo Musokotwane <br><p style="font-size:9px;  font-weight:300">Chief Finacial Officer</p></div>
        </div>
                 
                    
    </div> 
             
    </div>

