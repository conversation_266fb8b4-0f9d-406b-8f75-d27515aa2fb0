
<style>

  .ex1{
     transition: all 1.5s;
     animation: animation-name 0.5s linear;
  }
  
  @keyframes animation-name{
     from{
     transform: translateX(-100%);
     height: 0px;
     }
  }
  </style>



<.form :let={f} for={@changeset} as={:loan_product} id="loan-product-form" phx-submit="save" phx-change="validate" phx-target={@myself}>
    <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow ex1">
      <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
        <div class="-ml-4 -mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
          <div class="ml-4 mt-4">
            <div class="flex items-center">
              <span class="inline-block h-10 w-10 overflow-hidden rounded-full bg-gray-100">
                <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </span>
  
              <div class="ml-4">
                <h3  class="text-base font-semibold leading-6 text-gray-900">Create Loan Product</h3>
                <p class="text-sm text-gray-500">
                  <a href="#">Loan Product Details</a>
                </p>
              </div>
            </div>
          </div>
          <div class="ml-4 mt-4 flex flex-shrink-0">

            <.link navigate={Routes.loan_product_index_path(@socket,:index)} class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100 " role="menuitem" tabindex="-1" id="menu-item-0">
            <button type="button" class="relative inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="-ml-0.5 mr-1.5 h-5 w-5 text-gray-400">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"
                />
              </svg>
              <span>View Loan Products</span>
            </button>
            </.link>
          </div>
        </div>
      </div>
  
      <div class="px-4 py-5 sm:p-6">
        <div class="space-y-12">
          <div class="border-b border-gray-900/10 pb-12">
            <div class="mt-8 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
  
            <div class="sm:col-span-3">
                <label for="last-product_type" class="block text-sm font-medium leading-6 text-gray-900">Schedule type</label>
                <div class="mt-2">
                  <%= select(
                     f,
                     :schedule_type,
                     [
                     {"Schedule 02G", "02G"},
                     {"Schedule 05B", "05B"},
                     {"Schedule 07A", "07A"},
                     ],
                     prompt: [key: "Select Schedule type", disabled: true],
                     selected: input_value(f, :schedule_type),
                     autocomplete: "Schedule type",
                     class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                     required: "true"
                     ) %> <%= error_tag(f, :schedule_type) %>
                </div>
              </div>

             
  
              <div class="sm:col-span-3">
                <label for="product_type" class="block text-sm font-medium leading-6 text-gray-900">Product type</label>
                <div class="mt-2">
                  <%= text_input(
                    f,
                    :product_type,
                    autocomplete: "Product type",
                    placeholder: "Product type",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true"
                  ) %> <%= error_tag(f, :product_type) %>
                </div>
              </div>

               <div class="sm:col-span-3">
                <label for="security_value_ratio" class="block text-sm font-medium leading-6 text-gray-900">Security Value Ratio</label>
                <div class="mt-2">
                  <%= text_input(
                    f,
                    :security_value_ratio,
                    autocomplete: "Security Value Ratio",
                    placeholder: "Security Value Ratio",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true"
                  ) %> <%= error_tag(f, :security_value_ratio) %>
                </div>
              </div>

               <div class="sm:col-span-3">
                <label for="type_of_security" class="block text-sm font-medium leading-6 text-gray-900"> Type Of Security</label>
                <div class="mt-2">
                  <%= text_input(
                    f,
                    :type_of_security,
                    autocomplete: " Type Of Security",
                    placeholder: " Type Of Security",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true"
                  ) %> <%= error_tag(f, :type_of_security) %>
                </div>
              </div>
  
            </div>
          </div>
        </div>
  
        <div class="mt-6 flex items-center justify-end gap-x-6">

          <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
        </div>
      </div>
    </div>
</.form>
  