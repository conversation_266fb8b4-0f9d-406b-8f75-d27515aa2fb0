defmodule MisReportsWeb.RegulatoryCapitalLive.RegulatoryCapitalComponent do
  use MisReportsWeb, :live_component
  use PipeTo.Override
  alias MisReports.{Repo}
  alias MisReports.{Prudentials, Prudentials.RegulatoryCapital}
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
      <%= Phoenix.View.render(MisReportsWeb.RegulatoryCapitalView , "regulatory_capital.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.RegulatoryCapitalView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{regulatory_capital: regulatory_capital} = assigns, socket) do
    changeset = Prudentials.change_regulatory_capital(regulatory_capital)
    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)
    {:ok,
      socket
      |> assign(assigns)
      |> assign(:process_id, process_id)
      |> assign(:reference, reference)
      |> assign(:step_id, step_id)
      |> assign(:action, action)
      |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"regulatory_capital" => regulatory_capital}, socket) do
    changeset =
      socket.assigns.regulatory_capital
      |> RegulatoryCapital.changeset(regulatory_capital)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"regulatory_capital" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end
   @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      # Reject action
      "96" ->
        save_regulatory_capital(socket, :reject, params)

      # Approve action
      "97" ->
        save_regulatory_capital(socket, :approve, params)

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    audit_msg = "Created new Regulatory Capital Amount \"#{params["regulatory_capital"]}\" for Month \"#{params["date"]}\""
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)
    IO.inspect(socket.assigns, label: "socket assigns")

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:regulatory_capital, RegulatoryCapital.changeset(%RegulatoryCapital{maker_id: user.id}, params))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
    {:ok, %{regulatory_capital: regulatory_capital}} -> # Changed this line to pattern match correctly
          case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Regulatory capital Creation"
             ) do
          {:ok, reference_number} ->
            # Update branch with reference
            case Prudentials.update_regulatory_capital(regulatory_capital, %{reference: reference_number}) do
              {:ok, updated_regulatory_capital} ->
                {:noreply,
                 socket
                 |> put_flash(
                   :info,
                   "Regulatory capital created successfully. Reference: #{reference_number}"
                 )
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update branch reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Regulatory created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end
        {:error, %Ecto.Changeset{} = changeset} ->
          {:noreply, assign(socket, changeset: changeset)}
      end
  end

  def handle_save(socket, :edit, params) do
    regulatory_capital = socket.assigns.regulatory_capital
    socket
    |> handle_update(params, regulatory_capital)
    |> case do
      {:ok, regulatory_capital} ->
        {:noreply,
          socket
          |> put_flash(:info, "Regulatory capital updated successfully")
          |> push_redirect(to: Routes.regulatory_capital_index_path(socket, :edit, regulatory_capital))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  defp save_regulatory_capital(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Regulatory Rejected"

    # Add action_id from params or use default
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Regulatory rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject Regulatory: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp save_regulatory_capital(socket, :approve, params) do
    regulatory_capital = socket.assigns.regulatory_capital
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Regulatory Capital Approval"

    # First update the branch status
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      RegulatoryCapital.changeset(regulatory_capital, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_rc}} ->
        # Then call the workflow
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Regulatory Capital approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Regulatory Capital approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve branch")
         |> assign(:changeset, %{regulatory_capital.changeset | errors: failed_value.errors})}
    end
  end

  def handle_update(socket, params, regulatory_capital) do
    audit_msg = "Updated new Regulatory Capital Amount \"#{params["regulatory_capital"]}\" for Month \"#{params["date"]}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(:regulatory_capital, RegulatoryCapital.changeset(regulatory_capital, Map.merge(params, %{"status" => "D", "checker" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{regulatory_capital: regulatory_capital, audit_log: _user_log}} ->
        {:ok, regulatory_capital}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

end
