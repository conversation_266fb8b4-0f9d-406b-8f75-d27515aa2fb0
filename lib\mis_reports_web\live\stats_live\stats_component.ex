defmodule MisReportsWeb.StatsLive.StatsComponent do
  use MisReportsWeb, :live_component
  use PipeTo.Override
  alias MisReports.{Repo, Employees}
  alias MisReports.Employees.EmployeeStats
  alias MisReportsWeb.UserController
  alias MisReports.Workers.Utils
  # import MisReportsWeb.UserLive.Index, only: [put_conn_user: 1]

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= Phoenix.View.render(MisReportsWeb.StatsView, "new.html", assigns) %>
    </div>
    """
  end

  @impl true
  def update(%{stat: stat} = assigns, socket) do
    changeset = Employees.change_employee_stats(stat)


    {:ok,
     socket
     |> assign(assigns)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end
  @impl true
  def handle_event("province_value", params, socket) do

    province = params["province"]
    id = socket.assigns.stat.id
    stat = Employees.get_employee_stats!(id)
    values = format_current(stat, params["province"])

    {:noreply,
     socket
     |> assign(:province, String.to_atom(province))
     |> assign(:stat, stat)
     |> assign(:values, values)
    }
  end


  @impl true
  def handle_event("validate", %{"stat" => stat}, socket) do
    changeset =
      socket.assigns.stat
      |> EmployeeStats.changeset(stat)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"stat" => stat}, socket) do
    save_stat(socket, socket.assigns.action, stat)
  end

  def handle_event("update", params, socket) do
    stat = socket.assigns.stat
    socket
    |> update_stat(params, stat)
    |> case do
      {:ok, stat} ->
        {:noreply,
         socket
         |> put_flash(:info, "Stats updated successful")
         |> push_redirect(
          to: Routes.employee_stats_index_path(socket, :edit, stat)
         )}

      {:error, %Ecto.Changeset{} = _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update")
         |> push_redirect(
           to: Routes.employee_stats_index_path(socket, :edit, stat)
         )}
    end
  end

  def handle_event("new_action", params, socket) do
    IO.inspect(params, label: "New Action Params")
    current_user = socket.assigns.current_user
    reference = socket.assigns.current_user.reference
    # Assuming reference is available in the socket assigns
    initial_record = MisReports.Utilities.get_initial_record_details(reference)

    current_user_id = to_string(current_user.id)

    case Workflow.call_workflow(
           reference,
           1100,
           current_user_id,
           80,
           "",
           "",
           "Proceeding to next stage"
         ) do
      {:ok, _result} ->
        {:noreply,
         socket
         |> put_flash(:info, "Successfully proceeded to next stage")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to proceed: #{reason}")}
    end
  end

  def format_current(stat, province) do
    stat[String.to_atom(province)] |> MisReports.Workers.Utils.to_atomic_map()
  end

  defp save_stat(socket, :new, params) do
    user = socket.assigns.current_user

    audit_msg = "Created new Employee statictic: #{params["descript"]}"
    Ecto.Multi.new()
    |> Ecto.Multi.insert(:stat, fn _changes -> EmployeeStats.changeset(
      %EmployeeStats{maker_id: user.id, maker_date: Utils.schema_time()}, params)
    end)
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, _stat} ->
        {:noreply,
         socket
         |> put_flash(:info, "Employee statistics created successfully")
         |> push_redirect(to: Routes.employee_stats_index_path(socket, :new))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def update_stat(socket, stat_params, stat) do
    params = prep_changes(stat_params)
    audit_msg = "Initiated bank account update for \"#{stat.descript}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(:stat, EmployeeStats.changeset(stat, Map.merge(params, %{"status" => "D", "checker_id" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{stat: stat, audit_log: _user_log}} ->
        {:ok, stat}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def prep_changes(data) do
    # Remove the "province" key and return a map with the province as the key
    # and the rest of the data as the value
    value = Map.drop(data, ["province"])
    Map.put(%{}, data["province"], value)
  end



end
