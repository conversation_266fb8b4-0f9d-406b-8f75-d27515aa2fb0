
<.table page_size={@page_size} length_menu={@length_menu} table_info={@table_info} page_num={@page_num} total_pages={@total_pages } table_pages={@table_pages}>
  <:thead>
    <tr>
      <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
        <div class="flex items-center">
          Template name
          <a href="#" phx-click="file_spec_table_sort" phx-value-sort_by="temp_name" phx-value-sort_dir={reverse_sort_order(@sort_by, :temp_name)}>
            <%= table_sort_icon(@sort_by, :temp_name) %>
          </a>
        </div>
      </th>
      <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">
        <div class="flex items-center">
          Read line
          <a href="#" phx-click="file_spec_table_sort" phx-value-sort_by="read_line" phx-value-sort_dir={reverse_sort_order(@sort_by, :read_line)}>
            <%= table_sort_icon(@sort_by, :read_line) %>
          </a>
        </div>
      </th>
      <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">
        <div class="flex items-center">
          Date created
          <a href="#" phx-click="file_spec_table_sort" phx-value-sort_by="maker_dt" phx-value-sort_dir={reverse_sort_order(@sort_by, :maker_dt)}>
            <%= table_sort_icon(@sort_by, :maker_dt) %>
          </a>
        </div>
      </th>
      <th scope="col" class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">
        <div class="flex items-center">
          Status
          <a href="#" phx-click="file_spec_table_sort" phx-value-sort_by="auth_status" phx-value-sort_dir={reverse_sort_order(@sort_by, :auth_status)}>
            <%= table_sort_icon(@sort_by, :auth_status) %>
          </a>
        </div>
      </th>
      <th scope="col" class="relative whitespace-nowrap py-3.5 pl-3 pr-4 sm:pr-0">
        <span class="sr-only">Actions</span>
      </th>
    </tr>
  </:thead>
  <:tbody>
    <tr :if={@file_specs == []} class="border-t border-gray-200">
      <th colspan="7" scope="colgroup" class="bg-gray-50 py-2 pl-4 pr-3 text-center text-sm font-semibold text-gray-900 sm:pl-3">No data available</th>
    </tr>
    <tr :for={file_spec <- @file_specs} class="even:bg-gray-50">
      <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
        <%= if file_spec.temp_name == "cust_segmt" do %>
          GBM File
        <% end %>
        <%= if file_spec.temp_name == "cust_contribution" do %>
          Customer Contribution
        <% end %>
        <%= if file_spec.temp_name == "all_deal" do %>
          All Deal
        <% end %>
        <%= if file_spec.temp_name == "fx_cash" do %>
          Fx Cash Flow
        <% end %>
        <%= if file_spec.temp_name == "loan_ad" do %>
          Loan and advances
        <% end %>
        <%= if file_spec.temp_name == "relief_list" do %>
          Relief List
        <% end %>
        <%= if file_spec.temp_name == "government_exposure_list" do %>
          Government Exposure List
        <% end %>
        <%= if file_spec.temp_name == "credit_line_avbl" do %>
          Credit lines available to the Bank
        <% end %>
        <%= if file_spec.temp_name == "loan_sectors" do %>
          Sectors
        <% end %>
        <%= if file_spec.temp_name == "loan_scheme_codes" do %>
          Loan Scheme Codes
        <% end %>
        <%= if file_spec.temp_name == "obddr" do %>
          OBDDR
        <% end %>

        <%= if file_spec.temp_name == "large_loans" do %>
          Large Loans
        <% end %>

        <%= if file_spec.temp_name == "insider_lending" do %>
          Insider Lending
        <% end %>

        <%= if file_spec.temp_name == "credit_cards" do %>
          Credit Cards
        <% end %>

        <%= if file_spec.temp_name == "nostro" do %>
          Nostro
        <% end %>

        <%= if file_spec.temp_name == "days_past_due" do %>
          Days Past Due
        <% end %>
        
        <%= if file_spec.temp_name == "account_domicile_branch" do %>
          Account Domicile Branch
        <% end %>

        <%= if file_spec.temp_name == "gdp_file" do %>
          GDP File
        <% end %>

        <%= if file_spec.temp_name == "cproducts" do %>
          CMMP Products
        <% end %>

        <%= if file_spec.temp_name == "cbranches" do %>
          CMMP Branches
        <% end %>

      </td>
      <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500"><%= file_spec.read_line %></td>
      <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500"><%= Timex.format!(file_spec.maker_dt, "%d/%m/%Y %H:%M:%S", :strftime) %></td>
      <td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500">
        <%= if file_spec.auth_status == "A" do %>
          Active
        <% else %>
          Disabled
        <% end %>
      </td>
      <td class="relative whitespace-nowrap py-2 pl-3 pr-4 text-right text-sm sm:pr-0">
        <div class="relative px-5 pt-2">
          <button class="focus:ring-2 rounded-md focus:outline-none font-medium" phx-click={table_dropdown(file_spec.id)} phx-click-away={table_dropdown()} role="button" aria-label="option">
            <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
            </svg>
          </button>
          <div
            id={"dropdown-#{file_spec.id}"}
            class="tbl-dropdown absolute text-left right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="menu-button"
            tabindex="-1"
          >
            <div class="py-1" role="none">
              <.link navigate={Routes.mapping_index_path(@socket, :edit_spec, file_spec)} class="text-gray-700 flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1" id={"edit-file-spec-#{file_spec.id}"}>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                </svg>
                Edit
              </.link>
              <.link :if={file_spec.auth_status != "A"} href="#" data-id={file_spec.id} data-status="A" id={"update-status-#{file_spec.id}"} data-event="file_spec_update_status" phx-hook="Confirm" phx-click={MisReportsWeb.Modals.show_modal()} class="text-gray-700 flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                </svg>
                Activate
              </.link>
              <.link :if={file_spec.auth_status == "A"} href="#" data-id={file_spec.id} data-status="D" id={"update-status-#{file_spec.id}"} data-event="file_spec_update_status" phx-hook="Confirm" phx-click={MisReportsWeb.Modals.show_modal()} class="text-gray-700 flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
                Disable
              </.link>
              <.link href="#" data-id={file_spec.id} id={"update-status-#{file_spec.id}"} data-event="delete_file_spec" phx-hook="Confirm" phx-click={MisReportsWeb.Modals.show_modal()} class="text-gray-700 flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                </svg>
                Delete
              </.link>
            </div>
          </div>
        </div>
      </td>
    </tr>
  </:tbody>
</.table>

