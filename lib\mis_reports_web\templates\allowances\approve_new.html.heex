
<.form :let={f} for={@changeset} as={:allowances} id="allowances" phx-submit="save" phx-change="validate" phx-target={@myself} >
    <div id="alert-2" class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
    <div id="alert-2">
    <div class="px-4 py-6 sm:p-8">
       <div class="mt-5 grid grid-cols-4 gap-x-3  sm:grid-cols-3">
          <div class="sm:col-span-1 mt-3">
             <label for="last-name" class="block text-sm  leading-6 text-gray-900">Balance allowance account at beginning (Specific)</label>
             
                <div class="mt-2">
                   <%= text_input(
                  f,
                  :specific_bal_allowaance,
                  autocomplete: "Balance allowance account at beginning (Specific)",
                  placeholder: "Balance allowance account at beginning (Specific)",
                  class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                  disabled: "true"
                ) %>  <%= error_tag(f, :specific_bal_allowaance) %>
                </div>
             
          </div>
          <div class="sm:col-span-1 mt-3">
             <label for="last-name" class="block text-sm  leading-6 text-gray-900"> Add provision for loan losses (Specific)</label>
             <div class="mt-2">
 
                <%= text_input(
                   f,
                   :specific_add_provision,
                   autocomplete: "Regulatory Capital",
                   placeholder: "Add provision for loan losses (Specific)",
                   class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                   disabled: "true"
                 ) %>  <%= error_tag(f, :specific_add_provision) %>
 
 
                
             </div>
          </div>
          <div class="sm:col-span-1 mt-3">
             <label for="last-name" class="block text-sm  leading-6 text-gray-900">Exchange difference (Specific)</label>
             <div class="mt-2">
                <%= text_input(
                   f,
                   :specific_exchange_difference,
                   autocomplete: "Regulatory Capital",
                   placeholder: "Exchange difference (Specific)",
                   class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                   disabled: "true"
                 ) %>  <%= error_tag(f, :specific_exchange_difference) %>
               
             </div>
          </div>
          <div class="sm:col-span-1 mt-3">
             <label for="last-name" class="block text-sm  leading-6 text-gray-900">Allowance for Losses - Current Month Stage 3</label>
             <div class="mt-2">
                <%= text_input(
                   f,
                   :curr_allowance_for_loan_losses_stg_three,
                   autocomplete: "Allowance for Losses - Current Month Stage 3",
                   placeholder: "Allowance for Losses - Current Month Stage 3",
                   class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                   disabled: "true"
                 ) %>  <%= error_tag(f, :curr_allowance_for_loan_losses_stg_three) %>     
             </div>
          </div>
          <div class="sm:col-span-1 mt-3">
             <label for="last-name" class="block text-sm  leading-6 text-gray-900">Allowance for Losses - Previous Month Stage 3</label>
             <div class="mt-2">
                <%= text_input(
                   f,
                   :pre_allowance_for_loan_losses_stg_three,
                   autocomplete: "Allowance for Losses - Previous Month Stage 3",
                   placeholder: "Allowance for Losses - Previous Month Stage 3",
                   class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                   disabled: "true"
                 ) %>  <%= error_tag(f, :pre_allowance_for_loan_losses_stg_three) %>        
             </div>
          </div>
             
             <!--<div class="sm:col-span-1 mt-3">
             <label for="last-name" class="block text-sm  leading-6 text-gray-900">Add provision for loan losses (General)</label>
             <div class="mt-2">
                <input type="number" name="general_add_provision"  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" placeholder="General add provision">      
             </div>
             </div>-->
         
          <div class="sm:col-span-1 mt-3">
             <label for="first-name" class="block text-sm  leading-6 text-gray-900">Report Date</label>
             <div class="mt-2">
                <%= date_input(
                   f,
                   :report_date,
                   autocomplete: "Regulatory Capital",
                   placeholder: "Report Date",
                   class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                   disabled: "true"
                 ) %>   <%= error_tag(f, :report_date) %>
               
             </div>
          </div>
          <div class="col-span-full">
             <div class="sm:flex sm:items-center">
             </div>
             <div >
                <div class="col-span-full mt-5">
                   <div class="flex items-center justify-end gap-x-6">
                     <!--<button @click="forms.pop()" type="reset" name="reset" class="bg-white hover:bg-indigo-600 font-semibold  text-xs border border-gray-300 text-gray-800 py-2 px-4 rounded inline-flex items-center">
                         <svg class="fill-current w-4 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M4.755 10.059a7.5 7.5 0 0112.548-3.364l1.903 1.903h-3.183a.75.75 0 100 1.5h4.992a.75.75 0 00.75-.75V4.356a.75.75 0 00-1.5 0v3.18l-1.9-1.9A9 9 0 003.306 9.67a.75.75 0 101.45.388zm15.408 3.352a.75.75 0 00-.919.53 7.5 7.5 0 01-12.548 3.364l-1.902-1.903h3.183a.75.75 0 000-1.5H2.984a.75.75 0 00-.75.75v4.992a.75.75 0 001.5 0v-3.18l1.9 1.9a9 9 0 0015.059-4.035.75.75 0 00-.53-.918z"/>
                         </svg>
                         <span>Reset</span>
                      </button>
                      <button    @click="forms.push('')"class="bg-white hover:bg-indigo-600 font-semibold  text-xs border border-gray-300 text-gray-800 py-2 px-6 rounded inline-flex items-center">
                         <svg class="fill-current w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M12 5.25a.75.75 0 01.75.75v5.25H18a.75.75 0 010 1.5h-5.25V18a.75.75 0 01-1.5 0v-5.25H6a.75.75 0 010-1.5h5.25V6a.75.75 0 01.75-.75z"/>
                         </svg>
                         <span>Add column </span>
                      </button>-->
                   </div>
                </div><br>
                <hr style="width:100%;">
                <table>
                   <div class="mt-2">
                      <div class="mt-5 grid grid-cols-3 gap-x-3  sm:grid-cols-3" x-model="forms[index]">
                         <div class="sm:col-span-1 mt-3">
                            <label for="last-name" class="block text-sm  leading-6 text-gray-900">Description</label>
                            <div class="mt-2">
                               <%= text_input(
                                  f,
                                  :recoveries_description,
                                  autocomplete: "Description",
                                  placeholder: "Description",
                                  class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                                  disabled: "true"
                               ) %>  <%= error_tag(f, :recoveries_description) %>
                            </div>
                         </div>
                         <div class="mt-3">
                            <label for="last-name" class="block text-sm  leading-6 text-gray-900">Currency</label>
                            <%= select(
                               f,
                               :recoveries_currency,
                               [{"ZMW", "K"}, {"USD", "USD"} ],
                               selected: input_value(f, :recoveries_currency),
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               disabled: "true"
                               ) %> <%= error_tag(f, :recoveries_currency) %>
                             </div>
                         <div class="sm:col-span-1 mt-3">
                            <label for="last-name" class="block text-sm  leading-6 text-gray-900">Types of Adjustment</label>
                            <%= select(
                               f,
                               :type_adjust,
                               [{"Recoveries", "Recoveries"} ],
                               selected: input_value(f, :type_adjust),
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               disabled: "true"
                               ) %> <%= error_tag(f, :type_adjust) %>
                         </div>
                         <div class="sm:col-span-1 mt-3">
                            <label for="last-name" class="block text-sm  leading-6 text-gray-900">Amount</label>
                            <div class="mt-2">
                                  <%= text_input(
                                     f,
                                     :recoveries_amount,
                                     autocomplete: "Recoveries Amount",
                                     placeholder: "Recoveries Amount",
                                     class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                                     disabled: "true"
                                  ) %>  <%= error_tag(f, :recoveries_amount) %>
                            </div>
                         </div>
                         <div class="sm:col-span-1 mt-3">
                            <label for="last-name" class="block text-sm  leading-6 text-gray-900">Comment</label>
                            <div class="mt-2">
                               <div class="mt-2">
                                  <%= text_input(
                                     f,
                                     :recoveries_comment,
                                     autocomplete: "Comment",
                                     placeholder: "Comment",
                                     class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                                     disabled: "true"
                                  ) %>  <%= error_tag(f, :recoveries_comment) %>
                                  
                               </div>
                            </div>
                         </div>
                      </div>
                   </div>
                   <div class="mt-2">
                      <div class="mt-5 grid grid-cols-3 gap-x-3  sm:grid-cols-3" x-model="forms[index]">
                         <div class="sm:col-span-1 mt-3">
                            <div class="mt-2">
 
                               <%= text_input(
                                     f,
                                     :write_offs_description,
                                     autocomplete: "Description",
                                     placeholder: "Description",
                                     class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                                     disabled: "true"
                                  ) %>  <%= error_tag(f, :write_offs_description) %>
                              
                            </div>
                         </div>
                         <div class=" mt-3">
                             <%= select(
                               f,
                               :write_offs_currency,
                               [{"ZMW", "K"}, {"USD", "USD"} ],
                               selected: input_value(f, :write_offs_currency),
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               disabled: "true"
                               ) %> <%= error_tag(f, :write_offs_currency) %>
                           
                         </div>
                         <div class="sm:col-span-1 mt-3">
                             <%= select(
                               f,
                               :type_adjust,
                               [{"Write-off", "Write-off"}],
                               selected: input_value(f, :any_other_adjustment_currency),
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               disabled: "true"
                               
                               ) %> <%= error_tag(f, :any_other_adjustment_currency) %>
                         </div>
                         <div class="sm:col-span-1 mt-3">
                            <div class="mt-2">
                                     
                               <%= text_input(
                                     f,
                                     :write_offs_amount,
                                     autocomplete: "Write Offs Amount",
                                     placeholder: "Write Offs Amount",
                                     class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                                     disabled: "true"
                                  ) %>  <%= error_tag(f, :write_offs_amount) %>
                            </div>
                         </div>
                         <div class="sm:col-span-1 mt-3">
                            <div class="mt-2">
 
                               <%= text_input(
                                     f,
                                     :write_offs_comment,
                                     autocomplete: "Comment",
                                     placeholder: "Comment",
                                     class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                                     disabled: "true"
                                  ) %>  <%= error_tag(f, :write_offs_comment) %>
                                 
                            </div>
                         </div>
                      </div>
                   </div>
                   <div class="mt-2">
                      <div class="mt-5 grid grid-cols-3 gap-x-3  sm:grid-cols-3" x-model="forms[index]">
                         <div class="sm:col-span-1 mt-3">
                            <div class="mt-2">
                               <div class="mt-2">
 
                                  <%= text_input(
                                     f,
                                     :any_other_adjustment_description,
                                     autocomplete: "Description",
                                     placeholder: "Description",
                                     class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                                     disabled: "true"
                                  ) %> <%= error_tag(f, :any_other_adjustment_description) %>    
                               </div>
                            </div>
                         </div>
                         <div class=" mt-3">
 
                            <%= select(
                               f,
                               :any_other_adjustment_currency,
                               [{"ZMW", "K"}, {"USD", "USD"} ],
                               selected: input_value(f, :any_other_adjustment_currency),
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" ,
                               disabled: "true"         
                               ) %>   <%= error_tag(f, :any_other_adjustment_currency) %>                             
                         </div>
                         <div class="sm:col-span-1 mt-3">
                            <div class="mt-2">
 
                               <%= select(
                               f,
                               :any_other_adjustment_currency,
                               [{"Any other adjustments", "Any other adjustments"} ],
                               selected: input_value(f, :any_other_adjustment_currency),
                               class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                               disabled: "true"
                               
                               ) %> <%= error_tag(f, :any_other_adjustment_currency) %>   
 
                              
                            </div>
                         </div>
                         <div class="sm:col-span-1 mt-5">
                            <div class="mt-2">         
                               <div class="mt-2">
 
                                  <%= text_input(
                                     f,
                                     :any_other_adjustment_amount,
                                     autocomplete: "Any Other Adjustment Amount",
                                     placeholder: "Any Other Adjustment Amount",
                                     class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                                     disabled: "true"
                                  ) %>  <%= error_tag(f, :any_other_adjustment_amount) %>   
 
 
                               </div>
                            </div>
                         </div>
                         <div class="sm:col-span-1 mt-5">
                            <div class="mt-2">         
                               <div class="mt-2">
 
                                  <%= text_input(
                                     f,
                                     :any_other_adjustment_comment,
                                     autocomplete: "Comment",
                                     placeholder: "Comment",
                                     class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                                     disabled: "true"
                                  ) %>  <%= error_tag(f, :any_other_adjustment_comment) %>   
 
 
                               </div>
                            </div>
                         </div>
                         
                      </div>
                   </div>
                </table>
             </div>
          </div>
       </div>
    </div>
    <div class="mt-6 flex items-center justify-end gap-x-6">
        <button 
          type="submit" 
          phx-click="save"
          phx-target={@myself}
          phx-value-action="97"
          class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600">
          Approve
        </button>

        <button 
          type="submit"
          phx-click="save"
          phx-target={@myself}
          phx-value-action="96"
          class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
          Reject
        </button>
      </div>
    </div>
    </div>
    </.form>
    <.confirm_modal /> <.info_notification /> <.error_notification />
    
    
    
     
 