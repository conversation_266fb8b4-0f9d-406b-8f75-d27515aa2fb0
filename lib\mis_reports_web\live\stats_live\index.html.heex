<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
    
  <.info :if={live_flash(@flash, :info)} flash={@flash} /> <.error :if={live_flash(@flash, :error)} flash={@flash} />
  
  <%= if @live_action in [:new, :edit] do %>
    <.live_component module={MisReportsWeb.StatsLive.StatsComponent} 
    id="new-user" 
    values={@values} 
    province={@province} 
    stat={@stat} 
    action={@live_action} 
    current_user={@current_user} />
  <% end %>

  <%= if @live_action == :index do %>
      <%= Phoenix.View.render(MisReportsWeb.StatsView, "list.html", assigns) %>
  <% end %>
  
  </div>
  
  <.confirm_modal />
  
  <.info_notification />
  
  <.error_notification />
  