defmodule MisReportsWeb.ShareHoldersLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.{Repo}
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Utilities
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.UserLiveAuth
  alias MisReports.Utilities.ShareHolders

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]
    if UserLiveAuth.authorize?(socket, opts) do

      socket =
       socket
        |> assign(:process_id, params["process_id"])
        |> assign(:reference, params["reference"])
        |> assign(:step_id, params["step_id"])


       {:noreply,
         socket
         |> apply_action(socket.assigns.live_action, params)}
     else
       UserLiveAuth.unauthorized(socket)
     end
  end

  defp apply_action(socket, :new, _params) do
    changeset = Utilities.change_share_holders(%ShareHolders{})
    socket
    |> assign(:changeset, changeset)
    |> assign(:share_holders, %ShareHolders{})
    |> assign(:action, :new)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    share_holders = Utilities.get_share_holders!(id)
    socket
    |> assign(:share_holders, share_holders)
    |> assign(:action, :edit)
  end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        # |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        share_holders = Utilities.get_share_holders_by_reference!(reference)

        socket
        |> assign(:share_holders, share_holders)
        |> assign(:changeset, Utilities.change_share_holders(share_holders))
        |> assign(:reference, reference)
    end
  end

  defp apply_action(socket, :index, _params), do: list_share_holders(socket)

  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :delete]
    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_share_holders()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_share_holders()}
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_share_holders()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:report_date, :inserted_at, :status, :maker_id], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)

    {:noreply,
     socket
     |> assign(sort_by: sort_by)
     |> list_share_holders()}
  end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    share_holders = Utilities.get_share_holders!(id)
    audit_msg = "changed status for template for share holders to: #{status}"
    user = socket.assigns.current_user

    if user.id == share_holders.maker_id do
      {:noreply,
       socket
       |> put_flash(:error, "You cannot approve your own submission. Please have another user review and activate it.")
       |> push_redirect(to: Routes.share_holders_index_path(socket, :index))}
    else
      audit_msg = "changed status for template for share holders to: #{status}"


    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      ShareHolders.changeset(share_holders, %{status: status, checker_id: user.id})
    )
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _loan_sector, audit_log: _audit_log}} ->
        {:noreply,
        socket
        |> put_flash(:info, "Operation Succesfull!")
        |> push_redirect(
          to: Routes.share_holders_index_path(socket, :index)
        )}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
      end
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    share_holders = Utilities.get_share_holders!(id)
    audit_msg = "Deleted Loan sector: "
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_share_holders, share_holders)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
    {:ok, %{del_share_holders: _loan_sector, audit_log: _audit_log}} ->
      {:noreply,
      socket
      |> put_flash(:info, "Share holders Deleted successfully!")
      |> push_redirect(to: Routes.share_holders_index_path(socket, :index))}

    {:error, failed_value} ->
      {:error, failed_value}
    end
  end

  defp list_share_holders(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->

          Utilities.list_share_holders(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, share_holder: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  def audit_log(multi, user_id, audit_msg) do
    Ecto.Multi.run(multi, :audit_log, fn repo, _changes_so_far ->
      MisReports.Audit.UserLog.changeset(%MisReports.Audit.UserLog{}, %{user_id: user_id, activity: audit_msg})
      |> repo.insert()
    end)
  end

  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"share_holders", "new"}

      act when act in ~w(edit)a ->
        {"share_holders", "edit"}

      act when act in ~w(update_status)a ->
        {"share_holders", "update_status"}

      act when act in ~w(delete)a ->
        {"share_holders", "delete"}

      act when act in ~w(index)a ->
        {"share_holders", "index"}

      _ ->
        {"share_holders", "unknown"}
    end
  end
end
