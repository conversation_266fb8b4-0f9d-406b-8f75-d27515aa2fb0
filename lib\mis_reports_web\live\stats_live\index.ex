defmodule MisReportsWeb.StatsLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.UserLiveAuth
  alias MisReports.Employees
  alias MisReports.Repo
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Employees.EmployeeStats
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:page, 1)
     |> assign(:page_size, 10)
     |> assign(:isearch, nil)
     |> assign(:action, nil)
     |> assign(:sort_by, {:asc, :id})
     |> assign(:length_menu, [10, 25, 50, 100, 300, 500, 1000])
     |> assign(:provinces, list_all_provinces())}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do
      {:noreply,
        socket
          |> apply_action(socket.assigns.live_action, params)
          |> assign(:provinces, list_all_provinces())}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :index, _params), do: list_stats(socket)

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:stat, %EmployeeStats{})
    |> assign(:values, nil)
    |> assign(:province, "")
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    stat = Employees.get_employee_stats!(id)
    values = format_current(stat, :lusaka)

    socket
    |> assign(page: %{prev: "Source Mappings", current: "Edit specification"})
    |> assign(:stat, stat)
    |> assign(:values, values)
    |> assign(:province, :lusaka)
  end

  @impl true
  def handle_event("next_prov", %{"province" => _params} , socket) do
    socket =
      socket
      |> assign(page: %{prev: "Income Statement", current: "Income Statement"})
      |> assign(:stat, "")
      |> assign(:province, "")
      |> assign(:values, "")

    {:noreply, socket}
  end



  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_stats()}
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_stats()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_stats()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:descript, :maker_id, :checker_id , :inserted_at, :status], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)

    {:noreply,
     socket
     |> assign(sort_by: sort_by)
     |> list_stats()}
  end

  def format_current(stat, _province) do
    stat.lusaka |> MisReports.Workers.Utils.to_atomic_map()
  end



  defp list_stats(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Employees.list_employee_stats(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  def page_name(:new), do: "New Stats"
  def page_name(:index), do: "Employee Statistics List"
  def page_name(:edit), do: "Employee Statistics Edit"

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end





  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"employee_stats", "new"}

      act when act in ~w(edit)a ->
        {"employee_stats", "edit"}

      act when act in ~w(update_status)a ->
        {"employee_stats", "update_status"}

      act when act in ~w(delete)a ->
        {"employee_stats", "delete"}

      act when act in ~w(index)a ->
        {"employee_stats", "index"}

      _ ->
        {"employee_stats", "unknown"}
    end
  end

  defp list_all_provinces do
    [:lusaka, :central, :eastern, :cb, :southern, :luapula, :western, :northern, :north_west, :muchinga]
  end
end
