<.form :let={f} for={@changeset}  as={:counterparty_securities} id="new-counteryparty-securities" phx-submit="save" phx-change="validate" phx-target={@myself}>
    <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
      <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
        <div class="-ml-4 -mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
          <div class="ml-4 mt-4">
            <div class="flex items-center">
              <div class="ml-4">
                <h3  class="text-base font-semibold leading-6 text-gray-900">Create/Edit Counteryparty Security </h3>
                <p class="text-sm text-gray-500">
                  <a href="#"></a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <div class="px-4 py-5 sm:p-6">
        <div class="space-y-12">
          <div class="border-b border-gray-900/10 pb-12">
            <div class="mt-8 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div class="sm:col-span-3">
                    <label for="province" class="block text-sm font-medium leading-6 text-gray-900">Type Of Security</label>
                    <div class="mt-2">
                      <%= select(
                        f,
                        :security_type,
                        ["Bonds",  "BondMMDiscount"],
                        prompt: [key: "Select security type", disabled: true],
                        selected: input_value(f, :security_type),
                        autocomplete: "security type",
                        class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                        required: "true",
                        disabled: "true"
                      ) %> <%= error_tag(f, :security_type) %>
                    </div>
                  </div>

                <div class="sm:col-span-3">
                    <label for="province" class="block text-sm font-medium leading-6 text-gray-900">Foreign Gov/Other Debt Securities</label>
                    <div class="mt-2">
                      <%= select(
                        f,
                        :foreign_and_other_debt_securities,
                        ["Foreign government", "Other debt securities"],
                        prompt: [key: "Select security type", disabled: true],
                        selected: input_value(f, :foreign_and_other_debt_securities),
                        autocomplete: "security type",
                        class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                        required: "true",
                        disabled: "true"
                      ) %> <%= error_tag(f, :foreign_and_other_debt_securitie) %>
                    </div>
                  </div>

                <div class="sm:col-span-3">
                    <label for="account_name" class="block text-sm font-medium leading-6 text-gray-900">Issuer Name</label>
                    <div class="mt-2">
                      <%= text_input(
                         f,
                         :issuer_name,
                         placeholder: "Issuer name",
                         class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                         required: "true",
                          disabled: "true"
                         ) %> <%= error_tag(f, :issuer_name) %>
                    </div>
                  </div>
  
              
  
              <div class="sm:col-span-3">
                <label for="report_dt" class="block text-sm font-medium leading-6 text-gray-900">Report date</label>
                <div class="mt-2">
                  <%= date_input(
                    f,
                    :report_date,
                    autocomplete: "Report Date",
                    placeholder: "Report Date",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %>
                </div>
              </div>
  
            </div>
          </div>
        </div>
  <%= if not @view_only do %>
        <div class="mt-6 flex items-center justify-end gap-x-6">
        <button 
          type="submit" 
          phx-click="save"
          phx-target={@myself}
          phx-value-action="97"
          class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600">
          Approve
        </button>

        <button 
          type="submit"
          phx-click="save"
          phx-target={@myself}
          phx-value-action="96"
          class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
          Reject
        </button>
      </div>
      <% end %>
      </div>
    </div>
  </.form>
  
  