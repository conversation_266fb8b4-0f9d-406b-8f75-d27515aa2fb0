
<.form :let={f} for={@changeset}  phx-submit="save" as={:repossed_prop} id="repossed_prop" phx-target={@myself}>
  <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
    <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6 ">
      <div class="-ml-4 -mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
        <div class="ml-4 mt-4">
          <div class="flex items-center">
            <span class="inline-block h-10 w-10 overflow-hidden rounded-full bg-gray-100">
              <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </span>
            <div class="ml-4">
              <h3 class="text-base font-semibold leading-6 text-gray-900">Repossessed Properties</h3>
            </div>
          </div>
        </div>

        <div class="ml-4 mt-4 flex flex-shrink-0">
        </div>
      </div>
    </div>

    <div class="px-4 py-5 sm:p-6">
      <div class="space-y-12">
        <div class="border-b border-gray-900/10 pb-12">
          <div class="mt-8 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">

            <div class="sm:col-span-3">
              <label for="last-name" class="block text-sm font-medium leading-6 text-gray-900">Name of borrower</label>
              <div class="mt-2">
                <%= text_input(
                  f,
                  :name_of_borrower,
                  autocomplete: "",
                  placeholder: "Name of borrower",
                  class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                  required: "true",
                    disabled: "true"
                ) %> 
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="last-name" class="block text-sm font-medium leading-6 text-gray-900">Nature of property repossessed</label>
              <div class="mt-2">
                <%= text_input(
                  f,
                  :nature_of_property_rep,
                  autocomplete: "",
                  placeholder: "Nature of property repossessed",
                  class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                  required: "true",
                    disabled: "true"
                ) %> 
                
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="first-name" class="block text-sm font-medium leading-6 text-gray-900">Location of the property</label>
              <div class="mt-2">

                <%= text_input(
                    f,
                    :location_of_the_property,
                    autocomplete: "",
                    placeholder: "Location of the property",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %> 
               
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="mobile" class="block text-sm font-medium leading-6 text-gray-900">Date repossessed</label>
              <div class="mt-2">
                <%= date_input(
                    f,
                    :date_rep,
                    autocomplete: "",
                    placeholder: "Date repossessed",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %> 
                
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="province" class="block text-sm font-medium leading-6 text-gray-900">Type of security held</label>
              <div class="mt-2">
                <%= text_input(
                    f,
                    :type_of_security_held,
                    autocomplete: "",
                    placeholder: "Type of security held",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %> 
                
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="town" class="block text-sm font-medium leading-6 text-gray-900">Owner of the property</label>
              <div class="mt-2">
                <%= select(
                  f,
                  :owner_of_the_property,
                  [{"Borrower", "Borrower"}, {"Third Party", "Third Party"}],
                  selected: input_value(f, :owner_of_the_property),
                  disabled: "true",
                  class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  ) %>      
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="phy_addr" class="block text-sm font-medium leading-6 text-gray-900">Type of facility</label>
              <div class="mt-2">
                <%= select(
                  f,
                  :type_of_facility,
                  [{"Term Loan", "Term Loan"}, {"Overdraft", "Overdraft"}, {"Scheme Loan", "Scheme Loan"}, {"Credit Card Debt", "Credit Card Debt"}, {"Unauthorised Over", "Unauthorised Over"}, {"Staff Loan", "Staff Loan"},{"Invoice Descounting", "Invoice Descounting"}, {"Mortage", "Mortage"}, {"Assert Finance Lease", "Assert Finance Lease"}, {"Working Capital Loan", "Working Capital Loan"}, {"Other", "Other"}],
                  selected: input_value(f, :type_of_facility),
                   disabled: "true",
                  class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  ) %>   
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="post_addr" class="block text-sm font-medium leading-6 text-gray-900">Amount outstanding</label>
              <div class="mt-2">

                <%= number_input(
                    f,
                    :amount_outstanding,
                    autocomplete: "",
                    placeholder: "Amount outstanding",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %> 
               
                
              </div>
            </div>


            <div class="sm:col-span-3">
              <label for="phone" class="block text-sm font-medium leading-6 text-gray-900"> Estimated market value </label>
              <div class="mt-2">

                <%= number_input(
                    f,
                    :estimated_market_value,
                    autocomplete: "",
                    placeholder: "Provisions",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true",
                    disabled: "true"
                  ) %> 
               
              </div>
            </div>

        

            <div class="sm:col-span-3">
                <label for="phone" class="block text-sm font-medium leading-6 text-gray-900">Provisions </label>
                <div class="mt-2">
                    <%= number_input(
                        f,
                        :provisions,
                        autocomplete: "",
                        placeholder: "Estimated market value",
                        class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                        required: "true",
                        disabled: "true"
                      ) %> 
                 
                </div>
              </div>
  
              <div class="sm:col-span-3">
                <label for="email" class="block text-sm font-medium leading-6 text-gray-900">Date of last valuation report</label>
                <div class="mt-2">  
                    <%= date_input(
                        f,
                        :date_of_last_valuation,
                        autocomplete: "",
                        placeholder: "Date of last valuation report",
                        class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                        required: "true",
                        disabled: "true"
                      ) %> 
                </div>
              </div>


              <div class="sm:col-span-3">
                <label for="repoer_date" class="block text-sm font-medium leading-6 text-gray-900">Report Date</label>
                <div class="mt-2">  
                    <%= date_input(
                        f,
                        :report_date,
                        autocomplete: "",
                        placeholder: "Report Date",
                        class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                        required: "true",
                        disabled: "true"
                      ) %> 
                </div>
              </div> 
          </div>
        </div>
      </div>

      <div class="mt-6 flex items-center justify-end gap-x-6">
            <button 
              type="submit" 
              phx-click="save"
              phx-target={@myself}
              phx-value-action="97"
              class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600">
              Approve
            </button>

            <button 
              type="submit"
              phx-click="save"
              phx-target={@myself}
              phx-value-action="96"
              class="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
              Reject
            </button>
      </div>
    </div>
  </div>
</.form>

<.confirm_modal /> <.info_notification /> <.error_notification />

